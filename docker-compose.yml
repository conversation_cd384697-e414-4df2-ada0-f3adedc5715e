
version: '3.8'

volumes:
  mongodb_data:
  redis_data:
  app_logs:
  building_blocks_data:

networks:
  backend:

services:
  mongodb:
    image: mongo:7
    container_name: retro_mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
    volumes:
      - mongodb_data:/data/db
    networks:
      - backend
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    container_name: retro_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - backend
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  retro-runner:
    build: .
    container_name: retro_synthesis_runner
    restart: always
    ports:
      - "8080:8080"  # Main application
      - "5555:5555"  # Flower monitoring
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - MONGODB_URL=************************************/
      - REDIS_URL=redis://redis:6379/0
      - ENABLE_FLOWER=true
    volumes:
      - app_logs:/app/logs
      - building_blocks_data:/app/data/building_blocks
      - ./bfs_interim_results:/app/bfs_interim_results
    networks:
      - backend
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "main.py", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
