"""
Main application entry point for Retro Synthesis Runner.
This module provides backward compatibility with the original main.py interface.
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.cli import main as cli_main

def main():
    """Main application entry point with backward compatibility."""
    # If no arguments provided, show help
    if len(sys.argv) == 1:
        print("Retro Synthesis Runner - Production Ready")
        print("Usage: python main.py [command]")
        print("Commands:")
        print("  worker    - Start Celery worker")
        print("  server    - Start web server")
        print("  health    - Run health checks")
        print("  validate  - Validate configuration")
        print("  status    - Show system status")
        print("  --help    - Show detailed help")
        return

    # Run the CLI
    cli_main()


if __name__ == "__main__":
    main()




