# Retro Synthesis Runner Configuration Template
# Copy this file to .env and update the values

# Database Configuration
MONGODB_URL=**************************************/
MONGODB_DATABASE=retro_synthesis

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# API Configuration
AZURE_DISCONNECTION_TAGGER_URL=https://your-endpoint.azureml.net/score
AZURE_DISCONNECTION_TAGGER_API_KEY=your-api-key

AZURE_REACTANT_GENERATOR_URL=https://your-endpoint.azureml.net/score
AZURE_REACTANT_GENERATOR_API_KEY=your-api-key

AZURE_RETRO_RANKER_URL=https://your-endpoint.azureml.net/score
AZURE_RETRO_RANKER_API_KEY=your-api-key

PARROT_URL=https://your-parrot-endpoint.com/predict
PARROT_KEY=your-parrot-api-key

ASKCOS_EM_URL=https://askcos.mit.edu/api/v2/retro/
ASKOS_PREDICTIONS_PISTACHO_URL=https://askcos.mit.edu/api/v2/atom-mapping/
ASKOS_PRE_REAXYS_URL=https://askcos.mit.edu/api/v2/retro/

REACTION_CLASS_URL=https://your-reaction-class-endpoint.com/classify

# Processing Configuration
MAX_DEPTH=3
BEAM_WIDTH=10
SCSCORE_THRESHOLD=1.25
PARALLEL_BFS_WORKERS=4
REACTANT_GENERATOR_BATCH_SIZE=50
MAX_ROUTES=400

# File Paths
BUILDING_BLOCKS_PATH=data/building_blocks/building_blocks.txt
DISCONNECTIONS_STORAGE_PATH=data/disconnections/

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/retro_synthesis.log

# Server Configuration
HOST=0.0.0.0
PORT=8080
DEBUG=false

# Monitoring
ENABLE_FLOWER=true
FLOWER_PORT=5555

# Azure Storage (optional)
AZURE_STORAGE_CONNECTION_STRING=your-connection-string
AZURE_CONTAINER_NAME=retro-synthesis-data
