# Retro Synthesis Runner Dockerfile
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies for RDKit, chemistry libraries, and other requirements
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpango-1.0-0 \
    libcairo2 \
    libpangoft2-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf-2.0-0 \
    libharfbuzz0b \
    libxrender1 \
    libxext6 \
    libsm6 \
    libglib2.0-0 \
    libfreetype6 \
    libpng16-16 \
    libexpat1 \
    fonts-dejavu-core \
    unzip \
    curl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs data/building_blocks bfs_interim_results

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Expose ports
EXPOSE 8030 5555

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python main.py health || exit 1

CMD ["/bin/bash", "./start.sh"]