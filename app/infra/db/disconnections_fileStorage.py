"""
Disconnections file storage management.
Original implementation with Azure Blob Storage support.
"""

import pandas as pd
import os
import json
import hashlib
import re
from pathlib import Path
from typing import Optional, List, Dict, Any
import logging
import uuid
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
import io

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DisconnectionsStorage:
    """
    A class to manage disconnections data storage using local parquet files.
    Organizes data by target SMILES in separate subdirectories.
    """
    
    def __init__(self, base_path: str = "molecules/"):
        """
        Initialize the storage manager.
        
        Args:
            base_path (str): Base directory for storing disconnections data
        """
        self.connection_string = os.environ.get('AZURE_BLOB_CONNECTION_STRING')
        self.container_name= os.environ.get('AZURE_BLOB_RETRO_DISCONNECTIONS', 'retrorunnerdisconnections')
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        self.container_client = self.blob_service_client.get_container_client(self.container_name)
        self.metadata_path = Path("local_cache")  # For local mapping cache
        self.metadata_path.mkdir(exist_ok=True)
        self.base_path = base_path
        # self.base_path = Path(base_path)
        # self.models_path = self.base_path / "models"
        # self.metadata_path = self.base_path / "metadata"
        
        # # Create base directories
        # self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.models_path.mkdir(parents=True, exist_ok=True)
        self.metadata_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Initialized storage at {self.base_path}")
    
    def _sanitize_smiles(self, smiles: str) -> str:
        """
        Sanitize SMILES string to create a valid directory name.
        
        Args:
            smiles (str): SMILES string
            
        Returns:
            str: Sanitized directory name
        """
        hash_suffix = hashlib.sha256(smiles.encode()).hexdigest()
        print("=================>" + hash_suffix)
        
        return hash_suffix
    
    def _get_target_directory(self, target_smiles: str) -> Path:
        """
        Get the directory path for a target SMILES.
        
        Args:
            target_smiles (str): Target SMILES string
            
        Returns:
            Path: Directory path for the target
        """
        sanitized_smiles = self._sanitize_smiles(target_smiles)
        target_dir = self.models_path / sanitized_smiles
        target_dir.mkdir(parents=True, exist_ok=True)
        return target_dir
    
    def _save_target_metadata(self, target_smiles: str, sanitized_name: str):
        """
        Save metadata mapping between original SMILES and sanitized directory name
        to Azure Blob Storage.
        
        Args:
            target_smiles (str): Original SMILES string
            sanitized_name (str): Sanitized directory name
        """
        metadata_blob_path = "molecules/smiles_mapping.json"
        blob_client = self.container_client.get_blob_client(metadata_blob_path)
        
        metadata = {}
        
        # Try to load existing metadata
        if blob_client.exists():
            try:
                stream = io.BytesIO()
                blob_client.download_blob().readinto(stream)
                stream.seek(0)
                metadata = json.load(io.TextIOWrapper(stream, encoding='utf-8'))
            except json.JSONDecodeError:
                logger.warning("Could not load existing metadata from blob, starting fresh")
            except Exception as e:
                logger.warning(f"Error reading metadata blob: {e}")

        # Add or update entry
        metadata[sanitized_name] = {
            'original_smiles': target_smiles,
            'created_at': pd.Timestamp.now().isoformat()
        }

        # Upload updated metadata
        try:
            metadata_bytes = json.dumps(metadata, indent=2).encode('utf-8')
            blob_client.upload_blob(metadata_bytes, overwrite=True)
            logger.info("Updated metadata mapping in Azure Blob")
        except Exception as e:
            logger.error(f"Failed to upload metadata to Azure Blob: {e}")

    
    def _get_blob_prefix(self, target_smiles: str, data_type: str = "disconnections") -> str:
        """
        Get blob prefix for different data types.

        Args:
            target_smiles: Target SMILES string
            data_type: Type of data - "disconnections", "ltr", or "parrot"

        Returns:
            str: Blob prefix path
        """
        sanitized = self._sanitize_smiles(target_smiles)

        if data_type == "disconnections":
            return f"{self.base_path}{sanitized}/disconnections/mttl_v3"
        elif data_type == "ltr":
            return f"{self.base_path}{sanitized}/disconnections/preprocessed_ltr_v3"
        elif data_type == "parrot":
            return f"{self.base_path}{sanitized}/parrot/predictions_v1"
        else:
            # Default to disconnections for backward compatibility
            return f"{self.base_path}{sanitized}/disconnections/mttl_v3"

    
    def store_data_to_blob(self, df: pd.DataFrame, target_smiles: str,
                        analysis_id: Optional[str] = None,
                        overwrite: bool = False, is_disconnections: bool = True,
                        data_type: Optional[str] = None) -> bool:
        try:
            # Determine data type from parameters
            if data_type is None:
                data_type = "disconnections" if is_disconnections else "ltr"

            blob_prefix = self._get_blob_prefix(target_smiles, data_type)
            # self._save_target_metadata(target_smiles, sanitized)

            df_copy = df.copy()
            df_copy['target_smiles'] = target_smiles
            df_copy['saved_at'] = pd.Timestamp.now()
            if analysis_id:
                df_copy['analysis_id'] = analysis_id

            # Generate filename
            file_id = str(uuid.uuid4()) if analysis_id is None else analysis_id
            filename = f"{file_id}.parquet"
            blob_path = f"{blob_prefix}/{filename}"

            # Check if exists
            blob_client = self.container_client.get_blob_client(blob_path)
            if blob_client.exists() and not overwrite:
                logger.warning(f"Blob {blob_path} already exists.")
                return False

            # Convert df to parquet in memory
            buffer = io.BytesIO()
            df_copy.to_parquet(buffer, index=False)
            buffer.seek(0)

            blob_client.upload_blob(buffer, overwrite=overwrite)
            logger.info(f"Saved to Azure Blob: {blob_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving to Azure: {e}")
            return False

    
    def get_data_from_blob(self, target_smiles: str,
                        analysis_id: Optional[str] = None,
                        latest_only: bool = True,
                        is_disconnections: bool = True,
                        data_type: Optional[str] = None) -> pd.DataFrame:
        try:
            # Determine data type from parameters
            if data_type is None:
                data_type = "disconnections" if is_disconnections else "ltr"

            blob_prefix = self._get_blob_prefix(target_smiles, data_type)
            blobs = self.container_client.list_blobs(name_starts_with=blob_prefix)
            
            # Convert to list and filter for parquet files
            parquet_blobs = [b for b in blobs if b.name.endswith(".parquet")]
            
            if analysis_id:
                parquet_blobs = [b for b in parquet_blobs if analysis_id in b.name]

            if not parquet_blobs:
                logger.warning(f"No matching records found. {is_disconnections}")
                return pd.DataFrame()

            if latest_only:
                # Sort by last_modified and get the most recent blob
                latest_blob = max(parquet_blobs, key=lambda b: b.last_modified)
                return self._download_parquet(latest_blob.name)
            else:
                dfs = [self._download_parquet(b.name) for b in parquet_blobs]
                return pd.concat(dfs, ignore_index=True)

        except Exception as e:
            logger.error(f"Error loading from Azure: {e}")
            return pd.DataFrame()

    def _download_parquet(self, blob_name: str) -> pd.DataFrame:
        blob_client = self.container_client.get_blob_client(blob_name)
        stream = io.BytesIO()
        blob_data = blob_client.download_blob()
        blob_data.readinto(stream)
        stream.seek(0)
        return pd.read_parquet(stream)

    
    def query_disconnections(self, target_smiles: str,
                           sort_by: str = 'Prob_Forward_Prediction_1',
                           ascending: bool = False,
                           limit: Optional[int] = None,
                           filters: Optional[Dict[str, Any]] = None,
                           data_type: str = "disconnections") -> pd.DataFrame:
        """
        Query disconnections data with sorting and filtering.

        Args:
            target_smiles (str): Target SMILES string
            sort_by (str): Column to sort by
            ascending (bool): Sort order
            limit (int, optional): Maximum number of results
            filters (dict, optional): Column filters {'column': value}
            data_type (str): Type of data - "disconnections", "ltr", or "parrot"

        Returns:
            pd.DataFrame: Filtered and sorted results
        """
        try:
            df = self.get_data_from_blob(target_smiles, latest_only=False, data_type=data_type)
            
            if df.empty:
                return df
            
            # Apply filters
            if filters:
                for column, value in filters.items():
                    if column in df.columns:
                        if isinstance(value, (list, tuple)):
                            df = df[df[column].isin(value)]
                        else:
                            df = df[df[column] == value]
            
            # Sort
            if sort_by in df.columns:
                df = df.sort_values(by=sort_by, ascending=ascending)
            
            # Limit results
            if limit:
                df = df.head(limit)
            
            return df
            
        except Exception as e:
            logger.error(f"Error querying disconnections: {e}")
            return pd.DataFrame()

    def list_targets(self) -> List[str]:
        """
        List all target SMILES that have stored data.

        Returns:
            List[str]: List of target SMILES strings
        """
        try:
            metadata_file = self.metadata_path / "smiles_mapping.json"

            if not metadata_file.exists():
                # Fallback to directory listing
                target_dirs = [d for d in self.models_path.iterdir() if d.is_dir()]
                return [d.name for d in target_dirs]

            with open(metadata_file, 'r') as f:
                metadata = json.load(f)

            return [info['original_smiles'] for info in metadata.values()]

        except Exception as e:
            logger.error(f"Error listing targets: {e}")
            return []

    def get_target_info(self, target_smiles: str) -> Dict[str, Any]:
        """
        Get information about a target's stored data.

        Args:
            target_smiles (str): Target SMILES string

        Returns:
            Dict[str, Any]: Information about the target
        """
        try:
            target_dir = self._get_target_directory(target_smiles)
            summary_file = target_dir / "summary.json"

            if summary_file.exists():
                with open(summary_file, 'r') as f:
                    return json.load(f)

            # Generate summary if not exists
            parquet_files = list(target_dir.glob("*.parquet"))
            total_records = 0

            for file in parquet_files:
                df = pd.read_parquet(file)
                total_records += len(df)

            return {
                'target_smiles': target_smiles,
                'total_files': len(parquet_files),
                'total_records': total_records
            }

        except Exception as e:
            logger.error(f"Error getting target info: {e}")
            return {}

    def delete_target_data(self, target_smiles: str, confirm: bool = False) -> bool:
        """
        Delete all data for a target SMILES.

        Args:
            target_smiles (str): Target SMILES string
            confirm (bool): Confirmation flag for safety

        Returns:
            bool: True if successful, False otherwise
        """
        if not confirm:
            logger.warning("Delete operation requires confirm=True")
            return False

        try:
            target_dir = self._get_target_directory(target_smiles)

            if target_dir.exists():
                import shutil
                shutil.rmtree(target_dir)
                logger.info(f"Deleted data for target: {target_smiles}")
                return True
            else:
                logger.warning(f"No data found for target: {target_smiles}")
                return False

        except Exception as e:
            logger.error(f"Error deleting target data: {e}")
            return False


