"""
Health check HTTP handler and server.
"""

import json
import time
import socket
import threading
from http import HTT<PERSON>tatus
from http.server import <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseHTTPRequestHandler, ThreadingHTTPServer
from typing import Optional

from app.core.logging import get_logger

logger = get_logger(__name__)


class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP handler for health check endpoints."""
    
    def do_GET(self):
        """Handle GET requests."""
        if self.path == '/health':
            self._handle_health_check()
        elif self.path == '/ready':
            self._handle_readiness_check()
        else:
            self._handle_not_found()
    
    def _handle_health_check(self):
        """Handle health check endpoint."""
        try:
            data = {
                "status": "healthy",
                "timestamp": time.time(),
                "service": "retro-synthesis-runner"
            }
            self._send_json_response(HTTPStatus.OK, data)
        except Exception as e:
            logger.error(f"Health check error: {e}")
            data = {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time(),
                "service": "retro-synthesis-runner"
            }
            self._send_json_response(HTTPStatus.INTERNAL_SERVER_ERROR, data)
    
    def _handle_readiness_check(self):
        """Handle readiness check endpoint."""
        try:
            # Add more sophisticated readiness checks here
            # e.g., database connectivity, Redis connectivity, etc.
            data = {
                "status": "ready",
                "timestamp": time.time(),
                "service": "retro-synthesis-runner"
            }
            self._send_json_response(HTTPStatus.OK, data)
        except Exception as e:
            logger.error(f"Readiness check error: {e}")
            data = {
                "status": "not_ready",
                "error": str(e),
                "timestamp": time.time(),
                "service": "retro-synthesis-runner"
            }
            self._send_json_response(HTTPStatus.SERVICE_UNAVAILABLE, data)
    
    def _handle_not_found(self):
        """Handle 404 responses."""
        data = {
            "error": "Not Found",
            "path": self.path,
            "timestamp": time.time()
        }
        self._send_json_response(HTTPStatus.NOT_FOUND, data)
    
    def _send_json_response(self, status_code: HTTPStatus, data: dict):
        """Send JSON response."""
        try:
            self.send_response(status_code)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(data).encode())
        except (BrokenPipeError, ConnectionResetError, socket.error) as e:
            logger.warning(f"Client disconnected before response could be sent: {e}")
    
    def log_message(self, format, *args):
        """Override to suppress default logging."""
        return


class HealthCheckServer:
    """Health check HTTP server."""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8030):
        """
        Initialize health check server.
        
        Args:
            host: Server host
            port: Server port
        """
        self.host = host
        self.port = port
        self.server: Optional[HTTPServer] = None
        self.server_thread: Optional[threading.Thread] = None
        self._running = False
    
    def start(self):
        """Start the health check server."""
        if self._running:
            logger.warning("Health check server is already running")
            return
        
        try:
            self.server = ThreadingHTTPServer((self.host, self.port), HealthCheckHandler)
            self.server_thread = threading.Thread(
                target=self.server.serve_forever,
                name="HealthCheckServer",
                daemon=True
            )
            self.server_thread.start()
            self._running = True
            logger.info(f"Health check server started on {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to start health check server: {e}")
            raise
    
    def stop(self):
        """Stop the health check server."""
        if not self._running:
            return
        
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5)
            
            self._running = False
            logger.info("Health check server stopped")
        except Exception as e:
            logger.error(f"Error stopping health check server: {e}")
    
    def is_running(self) -> bool:
        """Check if server is running."""
        return self._running
