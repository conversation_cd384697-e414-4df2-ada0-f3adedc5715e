"""
Task manager for coordinating complex task operations.
"""

import time
from typing import Dict, Any
from celery import Task

from app.core.config import get_config
from app.core.logging import get_logger
from app.services.database_service import DatabaseService, RetroStatus
from app.workers.celery_app import get_celery_app

logger = get_logger(__name__)


class TaskManager:
    """Manager for coordinating task operations."""
    
    def __init__(self, config=None):
        """
        Initialize task manager.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or get_config()
        self.db_service = DatabaseService(config)
        self.celery_app = get_celery_app(config)
    
    def update_task_status(
        self,
        request_id: str,
        target_smiles: str,
        status: str,
        error_message: str = None
    ) -> None:
        """
        Update task status in database.
        
        Args:
            request_id: Request identifier
            target_smiles: Target SMILES string
            status: New status
            error_message: Optional error message
        """
        try:
            retro_status = RetroStatus(status)
            self.db_service.insert_log(
                request_id=request_id,
                input_type="SMILES",
                input_value=target_smiles,
                status=retro_status,
                error_message=error_message
            )
            logger.debug(f"Updated task status for {request_id} to {status}")
        except Exception as e:
            logger.error(f"Failed to update task status for {request_id}: {e}")
    
    def is_config_processed(
        self,
        request_id: str,
        config: Dict[str, Any]
    ) -> bool:
        """
        Check if configuration is already processed.
        
        Args:
            request_id: Request identifier
            config: Configuration to check
            
        Returns:
            True if already processed, False otherwise
        """
        return self.db_service.check_config_is_processed(request_id, config)
    
    def handle_task_error(
        self,
        error: Exception,
        request_id: str,
        target_smiles: str,
        molecule_name: str,
        task_instance: Task,
        start_time: float
    ) -> None:
        """
        Handle task error.
        
        Args:
            error: Exception that occurred
            request_id: Request identifier
            target_smiles: Target SMILES string
            molecule_name: Molecule name
            task_instance: Celery task instance
            start_time: Task start time
        """
        error_msg = str(error)
        logger.error(f"[ERROR] {request_id}: {error_msg}")
        
        # Update database with error status
        self.update_task_status(
            request_id=request_id,
            target_smiles=target_smiles,
            status="FAILED",
            error_message=error_msg
        )
        
        # Send error result to output queue
        self._send_error_result(
            request_id=request_id,
            molecule_name=molecule_name,
            error_msg=error_msg,
            task_instance=task_instance,
            start_time=start_time
        )
    
    def _send_error_result(
        self,
        request_id: str,
        molecule_name: str,
        error_msg: str,
        task_instance: Task,
        start_time: float
    ) -> None:
        """Send error result to output queue."""
        try:
            result_data = {
                "request_id": request_id,
                "molecule_name": molecule_name,
                "status": "FAILED",
                "error_message": error_msg,
                "failed_at": time.time(),
                "task_id": task_instance.request.id,
                "retries": task_instance.request.retries,
                "processing_time": time.time() - start_time
            }
            
            self.celery_app.send_task(
                'retro_result_handler',
                args=[result_data],
                queue=self.config.redis.retro_output_queue
            )
            
            logger.debug(f"Sent error result for request {request_id}")
            
        except Exception as e:
            logger.error(f"Failed to send error result for {request_id}: {e}")
