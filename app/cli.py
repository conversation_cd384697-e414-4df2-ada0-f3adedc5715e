"""
Command-line interface for the application.
"""

import sys
import signal
import argparse
from typing import Optional

from app.factory import create_app
from app.core.logging import setup_logging, get_logger
from app.core.config import get_config
from app.utils.monitoring import get_metrics_summary

logger = get_logger(__name__)


class ApplicationRunner:
    """Application runner with proper lifecycle management."""
    
    def __init__(self):
        """Initialize application runner."""
        self.app = None
        self._shutdown_requested = False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self._shutdown_requested = True
            if self.app:
                self.app.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run_worker(self, log_level: str = "INFO"):
        """
        Run Celery worker.
        
        Args:
            log_level: Logging level
        """
        setup_logging(level=log_level)
        logger.info("Starting Retro Synthesis Worker")
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Create and start application
        self.app = create_app()
        self.app.start()
        
        # Import and start Celery worker
        from app.workers.celery_app import get_celery_app
        celery_app = get_celery_app()
        
        # Import task modules to register tasks
        import app.workers.retro_worker
        import app.workers.enrich_worker
        
        logger.info("Celery worker starting...")
        
        try:
            # Start Celery worker
            celery_app.worker_main([
                'worker',
                '--loglevel=info',
                '--concurrency=1',
                '--pool=solo',  # Use solo pool for better debugging
                '--without-gossip',
                '--without-mingle',
                '--without-heartbeat'
            ])
        except KeyboardInterrupt:
            logger.info("Worker interrupted by user")
        except Exception as e:
            logger.error(f"Worker error: {e}")
        finally:
            if self.app:
                self.app.stop()
    
    def run_health_server(self, port: int = 8030, log_level: str = "INFO"):
        """
        Run health check server only.
        
        Args:
            port: Server port
            log_level: Logging level
        """
        setup_logging(level=log_level)
        logger.info(f"Starting Health Check Server on port {port}")
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Create and start application
        self.app = create_app()
        self.app.setup_health_check(port=port)
        self.app.start()
        
        try:
            # Keep the server running
            while not self._shutdown_requested:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Health server interrupted by user")
        finally:
            if self.app:
                self.app.stop()
    
    def show_status(self):
        """Show application status."""
        try:
            config = get_config()
            print("=== Retro Synthesis Runner Status ===")
            print(f"Database URL: {config.database.mongo_url}")
            print(f"Redis URL: {config.redis.redis_url}")
            print(f"Use Celery: {config.use_celery}")
            print(f"Max Depth: {config.processing.max_depth}")
            print(f"Beam Width: {config.processing.beam_width}")
            print(f"Max Routes: {config.processing.max_routes}")
            print("\nQueues:")
            print(f"  Retro Input: {config.redis.retro_input_queue}")
            print(f"  Retro Output: {config.redis.retro_output_queue}")
            print(f"  Enrich Input: {config.redis.enrich_input_queue}")
            print(f"  Enrich Output: {config.redis.enrich_output_queue}")
            
            # Show metrics if available
            try:
                metrics_summary = get_metrics_summary()
                if metrics_summary:
                    print(f"\n{metrics_summary}")
            except Exception:
                pass
                
        except Exception as e:
            print(f"Error getting status: {e}")
            sys.exit(1)
    
    def validate_config(self):
        """Validate configuration."""
        try:
            config = get_config()
            config.validate()
            print("✓ Configuration is valid")
            
            # Test database connection
            from app.services.database_service import DatabaseService
            db_service = DatabaseService(config)
            print("✓ Database connection successful")
            
            # Test Redis connection
            import redis
            r = redis.from_url(config.redis.redis_url)
            r.ping()
            print("✓ Redis connection successful")
            
            print("All systems operational!")
            
        except Exception as e:
            print(f"✗ Configuration validation failed: {e}")
            sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="Retro Synthesis Runner")
    parser.add_argument(
        "command",
        choices=["worker", "health", "status", "validate"],
        help="Command to run"
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8030,
        help="Port for health server (default: 8030)"
    )
    
    args = parser.parse_args()
    
    runner = ApplicationRunner()
    
    if args.command == "worker":
        runner.run_worker(log_level=args.log_level)
    elif args.command == "health":
        runner.run_health_server(port=args.port, log_level=args.log_level)
    elif args.command == "status":
        runner.show_status()
    elif args.command == "validate":
        runner.validate_config()


if __name__ == "__main__":
    main()
