"""
Main tree search engine for retro-synthesis.
Updated to use the BFS engine as the primary search algorithm.
"""

from typing import Optional, List, Dict, Any
from app.core.config import get_config
from app.engines.bfs_tree_search_engine import BFS_TreeSearchEngine
from app.core.tree.tree_nodes import MoleculeNode, ReactionNode, NodeStatus
from app.services.database_service import DatabaseService
from app.core.logging import get_logger
from app.workers.enrich_worker import send_enrich_inflow_task
import time

logger = get_logger(__name__)


class TreeSearchEngine:
    """
    Main engine for retro-synthesis tree search.
    This is a wrapper around the BFS_TreeSearchEngine for backward compatibility.
    """
    
    def __init__(
        self,
        single_step_api=None,
        synthesis_score_api=None,
        config=None,
        kwargs: Optional[dict] = None
    ):
        self.config = config or get_config()
        self.single_step_api = single_step_api  # Not used in BFS engine
        self.synthesis_score_api = synthesis_score_api  # Not used in BFS engine
        self.db_service = DatabaseService()
        self.kwargs = kwargs or {}
        
        # Initialize the BFS engine as the main search engine
        self.bfs_engine = BFS_TreeSearchEngine(config=self.config)
        
        logger.info("TreeSearchEngine initialized with BFS engine backend")

    def _setup_components(self):
        """Setup components - not needed as BFS engine handles this internally."""
        pass
    
    def find_synthesis_routes(
        self,
        target_smiles: str,
        max_routes: int = None,
        kwargs: Optional[dict] = None
    ) -> List[List[ReactionNode]]:
        """
        Find the best synthesis routes for a target molecule using the BFS engine.
        
        Args:
            target_smiles: SMILES of target molecule
            max_routes: Maximum number of routes to return
            kwargs: Additional parameters
            
        Returns:
            List of synthesis routes (each route is a list of ReactionNodes)
        """
        logger.info(f"TreeSearchEngine: Finding synthesis routes for {target_smiles}")
        
        # Delegate to the BFS engine
        return self.bfs_engine.find_synthesis_routes(
            target_molecule=target_smiles,
            max_routes=max_routes,
            kwargs=kwargs or {}
        )

    def get_best_routes_from_checkpoint(self, checkpoint_id: str, max_routes: int = None) -> List[List[ReactionNode]]:
        """
        Load checkpoint and extract best routes without continuing search.
        Note: Checkpointing is not implemented in the BFS engine yet.
        
        Args:
            checkpoint_id: ID of the checkpoint to load
            max_routes: Maximum number of routes to return
            
        Returns:
            List of best synthesis routes from the checkpoint
        """
        logger.warning("Checkpoint functionality not implemented in BFS engine")
        return []

    def store_pathways(self, target_smiles: str, routes: List[List[ReactionNode]], kwargs: dict = {}) -> dict:
        """
        Store synthesis pathways using the BFS engine.
        
        Args:
            target_smiles: Target molecule SMILES
            routes: List of synthesis routes
            kwargs: Additional parameters
            
        Returns:
            Dictionary with storage results
        """
        logger.info(f"TreeSearchEngine: Storing pathways for {target_smiles}")
        
        # Delegate to the BFS engine
        return self.bfs_engine.store_pathways(
            target_smiles=target_smiles,
            routes=routes,
            kwargs=kwargs
        )

    def run_search(self, target_smiles: str, kwargs: dict = {}) -> List[List[ReactionNode]]:
        """
        Run the complete search and storage pipeline.
        
        Args:
            target_smiles: Target molecule SMILES
            kwargs: Search parameters
            
        Returns:
            List of synthesis routes
        """
        logger.info(f"TreeSearchEngine: Running complete search for {target_smiles}")
        
        # Find routes
        routes = self.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=kwargs.get('max_routes'),
            kwargs=kwargs
        )
        
        if routes:
            # Store pathways
            self.store_pathways(
                target_smiles=target_smiles,
                routes=routes,
                kwargs=kwargs
            )
        
        return routes

    def search(self, target_smiles: str, max_routes: int = None, kwargs: dict = {}) -> List[List[ReactionNode]]:
        """
        Legacy method for backward compatibility.
        
        Args:
            target_smiles: Target molecule SMILES
            max_routes: Maximum number of routes
            kwargs: Additional parameters
            
        Returns:
            List of synthesis routes
        """
        return self.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=max_routes,
            kwargs=kwargs
        )
