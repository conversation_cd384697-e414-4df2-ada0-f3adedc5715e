"""
Retro synthesis runner.
Moved from retro_runner.py with updated imports.
"""

from app.engines.bfs_tree_search_engine import BFS_TreeSearchEngine
from app.core.config import get_config
from app.engines.tree_search_engine import TreeSearchEngine
from app.services.database_service import DatabaseService, RetroStatus
from app.core.logging import get_logger

logger = get_logger(__name__)

def retro_runner(target_smiles: str, request_id: str = None, kwargs: dict = {}):
    """
    Main retro synthesis runner function.
    
    Args:
        target_smiles: Target molecule SMILES
        request_id: Request identifier
        kwargs: Additional parameters
        
    Returns:
        Tuple of (total_routes, message)
    """
    db_service = DatabaseService()
    
    # Log config start
    if request_id:
        db_service.insert_config_history(request_id, kwargs, RetroStatus.RUNNING)
    
    config = get_config()
    
    # Create the tree search engine
    engine = BFS_TreeSearchEngine(config)
    engine.max_depth = kwargs.get('max_depth', config.processing.max_depth)
    
    # Find synthesis routes
    logger.info(f"Starting retro synthesis for: {target_smiles}")
    
    try:
        routes = engine.find_synthesis_routes(target_molecule=target_smiles, max_routes=400, kwargs=kwargs)
        
        # Store pathways for later analysis
        logger.info(f"Storing pathways for target SMILES: {target_smiles}")
        pathways = engine.store_pathways(target_smiles=target_smiles, routes=routes, kwargs=kwargs)
        
        total_routes = len(routes)
        message = "" if total_routes > 0 else "No routes found"
        
        # Log config completion
        if request_id:
            kwargs['total_routes'] = total_routes
            db_service.insert_config_history(request_id, kwargs, RetroStatus.COMPLETED)
        
        logger.info(f"Completed retro synthesis for {target_smiles}: {total_routes} routes found")
        
        return total_routes, message
        
    except Exception as e:
        logger.error(f"Error in retro synthesis for {target_smiles}: {e}")
        
        # Log config failure
        if request_id:
            db_service.insert_config_history(request_id, kwargs, RetroStatus.FAILED)
        
        return 0, f"Error: {str(e)}"

