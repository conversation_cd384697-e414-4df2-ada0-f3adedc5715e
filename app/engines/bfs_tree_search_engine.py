"""
Complete BFS Tree Search Engine for retro-synthesis.
Moved from treeSearchEngine_v2.py with updated imports.
"""

from app.core.config import get_config
from app.core.validators.terminal_checker import *
from app.core.tree.tree_nodes import MoleculeNode, ReactionNode, NodeStatus, NodeType

import concurrent.futures
import time
import pandas as pd
import requests
import json
import os

from typing import List, Tu<PERSON>, Dict
from collections import deque
from app.utils.chemistry import canonicalize_smiles
from app.utils.common import *
from app.services.database_service import DatabaseService
from app.services.azure.utils import AzureUtils
from app.workers.enrich_worker import send_enrich_inflow_task
from app.infra.db.disconnections_fileStorage import DisconnectionsStorage
from concurrent.futures import ThreadPoolExecutor, as_completed

# RDKit imports
from rdkit import RDLogger


from scscore import SCScorer

from app.core.logging import get_logger

logger = get_logger(__name__)

# Suppress verbose RDKit logging
RDLogger.DisableLog('rdApp.*')


def get_beam_width_for_depth(depth: int) -> int:
    """Returns the beam width based on the current search depth."""
    if depth == 0:
        return 12
    elif depth == 1 or depth == 2:
        return 12
    else:
        return 6


class SCScoreTerminalChecker:
    """Terminal checker based on SCScore threshold."""
    
    def __init__(self, scs_scorer, threshold: float = 1.25):
        self.scs_scorer = scs_scorer
        self.threshold = threshold
    
    def is_terminal(self, node: MoleculeNode) -> bool:
        """Check if molecule is terminal based on SCScore."""
        try:
            score = self.scs_scorer.get_score_from_smi(node.smiles)[1]
            return score <= self.threshold
        except Exception as e:
            logger.debug(f"SCScorer failed for SMILES '{node.smiles}'. Error: {e}")
        return False


class BFS_TreeSearchEngine:
    """Complete BFS-based tree search engine for retro-synthesis."""
    
    def __init__(self, config=None):
        """Initialize BFS tree search engine."""
        self.config = config or get_config()
        self.API_KEYS = {}
        self.PROCESSED_MOLECULE_CACHE = {}
        self.max_depth = self.config.processing.max_depth
        self._setup_components()
        self.disconnections_storage = DisconnectionsStorage()
        
    def _setup_components(self):
        """Setup engine components."""
        self.scs_scorer = SCScorer()
        
        self.depth_checker = DepthTerminalChecker(self.max_depth)
        self.scscore_checker = SCScoreTerminalChecker(self.scs_scorer, self.config.processing.scscore_threshold)
        self.bb_checker = BuildingBlockTerminalChecker(self.config.building_blocks_path)
        self.composite_checker = CompositeTerminalChecker([self.depth_checker, self.bb_checker, self.scscore_checker])
        self.db_service = DatabaseService()
        self.azure_utils = AzureUtils()
        self._initialize_api_keys()
    
    def _initialize_api_keys(self):
        """Initialize API keys."""
        logger.info("Initializing API keys...")
        self.API_KEYS["disconnection-tagger-v1"] = self.config.api.azure_disconnection_tagger_api_key
        self.API_KEYS["reactant-generator-v1"] = self.config.api.azure_reactant_generator_api_key
        self.API_KEYS["retro-ranker-v1"] = self.config.api.azure_retro_ranker_api_key
        logger.info("API keys initialized.")
    
    # --- API CALLING FUNCTIONS ---
    def predict_retrosynthesis_EM(self, smiles: str):
        """Predict retrosynthesis using EM API."""
        api_url = getattr(self.config.api, 'askcos_em_url', None)
        if not api_url:
            return {"results": [{"reactants": []}]}
        response = guarded_requests_post_with_retry(
            api_url, 
            headers={"Content-Type": "application/json"}, 
            payload={"smiles": [smiles], "reaction_set": "USPTO_FULL"},
            timeout=self.config.api.requests_timeout
        )
        return response.json()
    
    def predict_retrosynthesis_askscos_AT(self, smiles: str):
        """Predict retrosynthesis using AskCos AT API."""
        api_url = getattr(self.config.api, 'askos_predictions_pistacho_url', None)
        if not api_url:
            return [{"products": [], "scores": []}]
        response = guarded_requests_post_with_retry(
            api_url, 
            headers={"Content-Type": "application/json"}, 
            payload={"smiles": [smiles]},
            timeout=self.config.api.requests_timeout
        )
        return response.json()
    
    def predict_retrosynthesis_TR(self, smiles: str):
        """Predict retrosynthesis using TR API."""
        api_url = getattr(self.config.api, 'askos_pre_reaxys', None)
        if not api_url:
            return [{"reactants": [], "scores": []}]
        response = guarded_requests_post_with_retry(
            api_url, 
            headers={"Content-Type": "application/json"}, 
            payload={"smiles": [smiles]},
            timeout=self.config.api.requests_timeout
        )
        return response.json()

    def call_api_helper(self, func, *args, **kwargs):
        """Helper for API calls with error handling."""
        try: 
            return func(*args, **kwargs)
        except Exception as e: 
            logger.error(f"API call {func.__name__} failed: {e}")
            return None

    def process_molecule(self, target_smile: str, api_keys: Dict[str, str], path_str: str = "") -> List[Tuple[str, float]]:
        """
        Performs the full analysis pipeline for a single molecule, including batched
        calls to the reactant generator to handle large payloads.
        """
        log_msg = f"Processing: {path_str if path_str else target_smile}"
        # Use a simple lock for caching
        import threading
        if not hasattr(self.config, 'cache_lock'):
            self.config.cache_lock = threading.Lock()
        with self.config.cache_lock:
            if target_smile in self.PROCESSED_MOLECULE_CACHE:
                logger.info(f"{log_msg} (from CACHE)")
                return self.PROCESSED_MOLECULE_CACHE[target_smile]
        logger.info(log_msg)
        canonical_target = canonicalize_smiles(target_smile)

        # === 1. PARALLEL DISCONNECTION TAGGING ===
        with concurrent.futures.ThreadPoolExecutor(max_workers=4, thread_name_prefix="Tagger_Worker") as executor:
            future_em = executor.submit(self.call_api_helper, self.predict_retrosynthesis_EM, canonical_target)
            future_at = executor.submit(self.call_api_helper, self.predict_retrosynthesis_askscos_AT, canonical_target)
            future_tr = executor.submit(self.call_api_helper, self.predict_retrosynthesis_TR, canonical_target)
            headers_tagger = {"Content-Type": "application/json", "Authorization": f"Bearer {api_keys['disconnection-tagger-v1']}"}
            future_azure_tagger = executor.submit(guarded_requests_post_with_retry, self.config.api.azure_disconnection_tagger_url, headers=headers_tagger, payload={"smiles": canonical_target}, timeout=self.config.api.requests_timeout)
            
            dfs, raw_results = [], {"em": future_em.result(), "at": future_at.result(), "tr": future_tr.result()}
            
            if raw_results["em"]:
                smiles = ['.'.join(sorted(canonicalize_smiles(r).split('.'))) for r in raw_results["em"]['results'][0]['reactants']]
                dfs.append(pd.DataFrame({"precursors": smiles, "prob": 1.0, "tag_type": 'askcos_em'}))
            if raw_results["at"]:
                smiles = ['.'.join(sorted(canonicalize_smiles(r).split('.'))) for r in raw_results["at"][0]['products']]
                dfs.append(pd.DataFrame({"precursors": smiles, "prob": raw_results["at"][0]['scores'], "tag_type": 'askcos_at'}))
            if raw_results["tr"]:
                smiles = ['.'.join(sorted(canonicalize_smiles(r).split('.'))) for r in raw_results["tr"][0]['reactants']]
                dfs.append(pd.DataFrame({"precursors": smiles, "prob": raw_results["tr"][0]['scores'], "tag_type": 'askcos_tr'}))
            
            response_tagger = future_azure_tagger.result()
            taggings = response_tagger.json()

        # === 2. BATCHED AZURE REACTANT GENERATOR ===
        headers_gen = {"Content-Type": "application/json", "Authorization": f"Bearer {api_keys['reactant-generator-v1']}"}
        all_tagged_smiles = taggings['tagged_smiles']
        combined_disconnections_response = {'disconnections_tagged': {}}

        cached_disconnections_df = self.disconnections_storage.get_data_from_blob(target_smile)
        if cached_disconnections_df.empty:
            logger.info(f"Calling reactant generator for {len(all_tagged_smiles)} tagged SMILES in batches of {getattr(self.config.processing, 'reactant_generator_batch_size', 50)}.")
            
            batch_size = getattr(self.config.processing, 'reactant_generator_batch_size', 50)
            for i in range(0, len(all_tagged_smiles), batch_size):
                batch_smiles = all_tagged_smiles[i:i + batch_size]
                payload_gen = {"tagged_smiles": batch_smiles}
                
                logger.debug(f"  - Sending batch {i//batch_size + 1} with {len(batch_smiles)} items.")
                
                response_gen = guarded_requests_post_with_retry(
                    self.config.api.azure_reactant_generator_url, 
                    headers=headers_gen, 
                    payload=payload_gen,
                    timeout=self.config.api.requests_timeout
                )
                
                batch_results = response_gen.json()
                if 'disconnections_tagged' in batch_results:
                    combined_disconnections_response['disconnections_tagged'].update(batch_results['disconnections_tagged'])

            disconnections_response = combined_disconnections_response
        
            # === 3. DATA POST-PROCESSING AND PIVOTING ===
            rows = [{'tag_type': tt, 'tag_smiles': s} for tt, sl in taggings['tag_mapping'].items() for s in sl]
            tagging_df = pd.DataFrame(rows)
            rows = []
            for k, v in disconnections_response['disconnections_tagged'].items():
                for d, p in zip(v["disconnections"], v["probs"]):
                    rows.append({"tag_smiles": k, "precursors": d, "prob": p})
            
            disconnections_df = pd.DataFrame(rows).reset_index(drop=True)
            disconnections_df_t = pd.merge(disconnections_df, tagging_df, on='tag_smiles', how='left')
            
            dfs.append(disconnections_df_t)
            disconnections_all = pd.concat(dfs, ignore_index=True)
            self.disconnections_storage.store_data_to_blob(disconnections_all, target_smile)
        else:
            logger.info(f"Using cached disconnections for {target_smile}")
            disconnections_all = pd.DataFrame(cached_disconnections_df)

        if disconnections_all.empty: 
            return []
        
        pivot_df = disconnections_all.pivot_table(index='precursors', columns='tag_type', values='prob', aggfunc='max', fill_value=0).reset_index()
        for col in ['substructure_R1', 'substructure_R2']:
            if col not in pivot_df.columns: 
                pivot_df[col] = 0.0
        pivot_df['substructure_R'] = pivot_df[['substructure_R1', 'substructure_R2']].max(axis=1)
        pivot_df = pivot_df.rename(columns={'auto_tagging': 'retro_confidence_auto', 'substructure_R': 'retro_confidence_substructure_r2', 'systematic_tagging': 'retro_confidence_random', 'askcos_at': 'retro_confidence_at', 'askcos_tr': 'retro_confidence_tr', 'askcos_em': 'retro_confidence_em'})
        cols = ['precursors', 'retro_confidence_auto', 'retro_confidence_substructure_r2', 'retro_confidence_random', 'retro_confidence_at', 'retro_confidence_tr', 'retro_confidence_em']
        for col in cols:
            if col not in pivot_df.columns: 
                pivot_df[col] = 0.0
        
        # === 4. RETRO RANKING ===
        payload_ranker = {"target": taggings['canonical_smiles'], "precursors": pivot_df[cols].rename(columns={'precursors': 'smiles'}).to_dict(orient='records')}
        headers_ranker = {"Content-Type": "application/json", "Authorization": f"Bearer {api_keys['retro-ranker-v1']}"}

        # Get LTR score
        cached_ltr_df = self.disconnections_storage.get_data_from_blob(target_smile, is_disconnections=False)
        if cached_ltr_df.empty:
            response_ranker = guarded_requests_post_with_retry(self.config.api.azure_retro_ranker_url, headers=headers_ranker, payload=payload_ranker, timeout=self.config.api.requests_timeout)
            score_df = pd.DataFrame(response_ranker.json()['results'])
            score_df = score_df[~(score_df.target == score_df.precursors)]
            score_df = score_df.sort_values(by='ltr_score', ascending=False)
            self.disconnections_storage.store_data_to_blob(score_df, target_smile, is_disconnections=False)
            final_results = list(zip(score_df['precursors'], score_df['ltr_score']))
        else:
            logger.info(f"Using cached LTR scores for {target_smile}")
            score_df = cached_ltr_df
            final_results = list(zip(score_df['precursors'], score_df['ltr_score']))
        
        with self.config.cache_lock:
            self.PROCESSED_MOLECULE_CACHE[target_smile] = final_results
        return final_results

    # --- BFS WORKER AND MAIN ORCHESTRATOR ---
    def reconstruct_path_for_logging(self, leaf_node: MoleculeNode, nodes_by_smiles: Dict[str, MoleculeNode]) -> str:
        """Reconstruct path for logging purposes."""
        path = [leaf_node.smiles]
        current_node = leaf_node
        while current_node and current_node.parent:
            if isinstance(current_node.parent, ReactionNode):
                path.insert(0, current_node.parent.reaction_data.get('Retro', ''))
                current_node = current_node.parent.parent
            else:
                current_node = current_node.parent
        if len(path) == 1 and leaf_node.depth > 0:
            root_smiles = next(iter(nodes_by_smiles.keys()))
            return f"{root_smiles} -> {leaf_node.smiles}"
        return " -> ".join(path)

    def bfs_worker(self, node_to_process: MoleculeNode, api_keys: Dict[str, str], composite_checker, bb_checker, scscore_checker, all_nodes_map_copy: Dict[str, MoleculeNode], beam_width: int) -> Tuple[MoleculeNode, List[MoleculeNode]]:
        """BFS worker function."""
        path_for_logging = self.reconstruct_path_for_logging(node_to_process, all_nodes_map_copy)

        # Calculate synthesis score for the molecule if not already set
        if node_to_process.synthesis_score is None:
            try:
                is_valid, score = self.scs_scorer.get_score_from_smi(node_to_process.smiles)
                if is_valid:
                    node_to_process.synthesis_score = score
                else:
                    node_to_process.synthesis_score = 5.0  # Default penalty score
            except Exception as e:
                logger.warning(f"Failed to calculate synthesis score for {node_to_process.smiles}: {e}")
                node_to_process.synthesis_score = 5.0  # Default penalty score

        # Check terminal conditions and set the appropriate status
        if composite_checker.is_terminal(node_to_process):
            if bb_checker.is_terminal(node_to_process):
                node_to_process.status = NodeStatus.TERMINAL
                logger.info(f"Terminal leaf found (Building Block): {path_for_logging}")
            elif scscore_checker.is_terminal(node_to_process):
                node_to_process.status = NodeStatus.TERMINAL
                logger.info(f"Terminal leaf found (SCScore <= {self.config.processing.scscore_threshold}): {path_for_logging}")
            else: # Must be depth
                node_to_process.status = NodeStatus.TERMINAL
            return node_to_process, []

        try:
            node_to_process.status = NodeStatus.UNSOLVED
            ranked_reactions = self.process_molecule(node_to_process.smiles, api_keys, path_str=path_for_logging)
            node_to_process.status = NodeStatus.SOLVED

            for reaction_smiles, score in ranked_reactions:
                # Create reaction node with proper structure
                reaction_node = ReactionNode(
                    reaction_data={
                        'Retro': reaction_smiles,
                        'Score': score,
                        'rxn_string': f'{reaction_smiles}>>{node_to_process.smiles}',
                        'Target': node_to_process.smiles
                    },
                    reactants=[canonicalize_smiles(s) for s in reaction_smiles.split('.')],
                    reaction_score=score
                )

                # Add reaction as child to molecule node
                node_to_process.add_child(reaction_node)

            top_reactions = [child for child in node_to_process.children if isinstance(child, ReactionNode)][:beam_width]
            nodes_for_next_level = []

            for reaction in top_reactions:
                for reactant_smiles in reaction.reactants:
                    child_mol_node = MoleculeNode(smiles=reactant_smiles, depth=node_to_process.depth + 1)
                    # Add molecule as child to reaction node
                    reaction.add_child(child_mol_node)
                    nodes_for_next_level.append(child_mol_node)

            return node_to_process, nodes_for_next_level
        except Exception as e:
            logger.error(f"Permanently failed to process node {node_to_process.smiles}. Pruning branch. Error: {e}")
            node_to_process.status = NodeStatus.INVALID
            return node_to_process, []

    def save_tree_to_json(self, processed_nodes: List[MoleculeNode], filename: str):
        """Save tree to JSON file."""
        logger.info(f"Saving {len(processed_nodes)} processed nodes to {filename}...")
        try:
            # Convert nodes to serializable format using the old node structure
            serializable_nodes = []
            for node in processed_nodes:
                node_dict = {
                    'node_id': node.node_id,
                    'smiles': node.smiles,
                    'depth': node.depth,
                    'status': node.status.value,
                    'synthesis_score': node.synthesis_score,
                    'children': []
                }

                for child in node.children:
                    if isinstance(child, ReactionNode):
                        child_dict = {
                            'node_id': child.node_id,
                            'reaction_data': child.reaction_data,
                            'reactants': child.reactants,
                            'reaction_score': child.reaction_score,
                            'status': child.status.value,
                            'children': []
                        }

                        for grandchild in child.children:
                            if isinstance(grandchild, MoleculeNode):
                                grandchild_dict = {
                                    'node_id': grandchild.node_id,
                                    'smiles': grandchild.smiles,
                                    'depth': grandchild.depth,
                                    'status': grandchild.status.value,
                                    'synthesis_score': grandchild.synthesis_score
                                }
                                child_dict['children'].append(grandchild_dict)

                        node_dict['children'].append(child_dict)

                serializable_nodes.append(node_dict)

            with open(filename, 'w') as f:
                json.dump(serializable_nodes, f, indent=2)
            logger.info("Save successful.")
        except Exception as e:
            logger.error(f"Failed to save tree to JSON: {e}")

    def extract_paths(self, all_processed_nodes: Dict[str, MoleculeNode]):
        """Extract synthesis paths for logging."""
        logger.info("--- Extracting Synthesis Paths ---")

        # Identify all valid terminal leaves
        reportable_leaves = []
        for node in all_processed_nodes.values():
            if node.status == NodeStatus.TERMINAL:
                reportable_leaves.append(node)
            # Also consider a route complete if it reaches the maximum depth and has not failed
            elif node.depth == self.max_depth and node.status != NodeStatus.INVALID:
                reportable_leaves.append(node)

        if not reportable_leaves:
            logger.info("No complete synthesis routes were found.")
            return

        logger.info(f"Found {len(reportable_leaves)} reportable terminal leaves (Building Blocks or Max Depth).")

        unique_routes = set()
        for leaf in reportable_leaves:
            path = []
            current_node = leaf
            while current_node and current_node.parent:
                path.append(current_node.smiles)
                if isinstance(current_node.parent, ReactionNode):
                    path.append(f"<-({current_node.parent.reaction_data.get('Retro', '')})")
                    # Find the parent molecule node
                    current_node = current_node.parent.parent
                else:
                    current_node = current_node.parent
            unique_routes.add(tuple(path[::-1]))

        logger.info(f"Discovered {len(unique_routes)} unique synthesis pathways:")
        for i, path_tuple in enumerate(sorted(list(unique_routes))):
            logger.info(f"  Route #{i+1}: {' '.join(path_tuple)}")

    def extract_routes_for_scoring(self, all_processed_nodes: Dict[str, MoleculeNode]) -> List[List[ReactionNode]]:
        """
        Extract synthesis routes in the format expected by the old scoring system.
        Returns a list of routes, where each route is a list of ReactionNodes.
        """
        logger.info("--- Extracting Routes for Scoring ---")

        # Find all terminal nodes
        terminal_nodes = []
        for node in all_processed_nodes.values():
            if node.status == NodeStatus.TERMINAL:
                terminal_nodes.append(node)
            elif node.depth == self.max_depth and node.status != NodeStatus.INVALID:
                terminal_nodes.append(node)

        if not terminal_nodes:
            logger.info("No terminal nodes found for route extraction.")
            return []

        routes = []
        seen_routes = set()  # Track unique routes by their retro_smiles sequence

        for terminal_node in terminal_nodes:
            route = self._extract_single_route(terminal_node)
            if route:
                # Create a unique identifier for this route based on retro_smiles
                route_signature = tuple(reaction.reaction_data.get('Retro', '') for reaction in route)

                if route_signature not in seen_routes:
                    seen_routes.add(route_signature)
                    routes.append(route)
                else:
                    logger.debug(f"Skipping duplicate route: {' -> '.join(route_signature)}")

        logger.info(f"Extracted {len(routes)} synthesis routes for scoring.")
        return routes

    def _extract_single_route(self, terminal_node: MoleculeNode) -> List[ReactionNode]:
        """
        Extract a single synthesis route from a terminal node to the root.
        Returns a list of ReactionNodes representing the route.
        """
        route = []
        current_node = terminal_node

        while current_node and current_node.parent:
            if isinstance(current_node.parent, ReactionNode):
                route.append(current_node.parent)
                current_node = current_node.parent.parent
            else:
                current_node = current_node.parent

        # Reverse the route to go from root to terminal
        return route[::-1]

    def score_route(self, route: List[ReactionNode]) -> float:
        """
        Score a synthesis route using the same logic as the old LeafNodeSynthesisScoreRouteScorer.
        Lower scores = better routes.
        """
        # Identify leaf nodes (reactants that are not products of any reaction)
        products = set()
        for reaction in route:
            products.add(reaction.reaction_data.get('Target', ''))

        leaf_reactants = []
        for reaction in route:
            for reactant_node in reaction.get_reactant_nodes():
                if reactant_node.smiles not in products:
                    if reactant_node.synthesis_score is not None:
                        leaf_reactants.append(reactant_node.synthesis_score)
                    else:
                        # Calculate synthesis score if not available
                        try:
                            is_valid, score = self.scs_scorer.get_score_from_smi(reactant_node.smiles)
                            if is_valid:
                                reactant_node.synthesis_score = score
                                leaf_reactants.append(score)
                            else:
                                leaf_reactants.append(5.0)  # Default penalty
                        except Exception as e:
                            logger.warning(f"Failed to calculate synthesis score for {reactant_node.smiles}: {e}")
                            leaf_reactants.append(5.0)  # Default penalty

        if len(leaf_reactants) > 0:
            max_score = max(leaf_reactants)  # Return the highest (worst) score
            return max_score
        else:
            return 100.0  # Default penalty score

    def run_bfs_retrosynthesis(self, target_molecule: str, results_dir: str = "bfs_interim_results"):
        """Run BFS retrosynthesis search."""
        logger.info(f"--- Starting Beam Search for: {target_molecule} ---")
        try:
            #Check if self.API_KEYS is empty
            if not self.API_KEYS:
                self._initialize_api_keys()
        except Exception as e:
            logger.critical(f"FATAL: Failed to authenticate. Please run 'az login'. Error: {e}")
            return

        self.PROCESSED_MOLECULE_CACHE.clear()
        os.makedirs(results_dir, exist_ok=True)

        root_node = MoleculeNode(smiles=target_molecule, depth=0)
        queue = deque([root_node])
        all_processed_nodes = {root_node.smiles: root_node}

        current_depth = 0
        while queue and current_depth < self.max_depth:
            level_size = len(queue)
            if level_size == 0: break
            logger.info(f"--- Processing Depth {current_depth}, {level_size} molecules in beam with max depth {self.max_depth} ---")

            nodes_at_current_level = [queue.popleft() for _ in range(level_size)]
            next_level_candidates = []
            processed_nodes_from_level = []

            beam_width = get_beam_width_for_depth(current_depth)
            logger.info(f"Using beam width of {beam_width} for this level.")

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.processing.parallel_bfs_workers, thread_name_prefix="BFS_Worker") as executor:
                all_nodes_copy = all_processed_nodes.copy()
                # Pass all three checkers to the worker
                future_to_node = {
                    executor.submit(self.bfs_worker, node, self.API_KEYS, self.composite_checker, self.bb_checker, self.scscore_checker, all_nodes_copy, beam_width): node
                    for node in nodes_at_current_level
                }
                for future in concurrent.futures.as_completed(future_to_node):
                    processed_node, children_for_next_level = future.result()
                    processed_nodes_from_level.append(processed_node)
                    all_processed_nodes[processed_node.smiles] = processed_node
                    next_level_candidates.extend(children_for_next_level)

            unique_next_level_smiles = set()
            for candidate_node in next_level_candidates:
                if candidate_node.smiles not in unique_next_level_smiles:
                    if candidate_node.smiles not in all_processed_nodes:
                        all_processed_nodes[candidate_node.smiles] = candidate_node
                    queue.append(all_processed_nodes[candidate_node.smiles])
                    unique_next_level_smiles.add(candidate_node.smiles)

            self.save_tree_to_json(processed_nodes_from_level, os.path.join(results_dir, f"depth_{current_depth}_results.json"))
            current_depth += 1

        logger.info("--- Beam Search Complete ---")
        return all_processed_nodes

    def find_synthesis_routes(self, target_molecule: str, max_routes: int = None, kwargs: dict = {}) -> List[List[ReactionNode]]:
        """
        Find the best synthesis routes for a target molecule.
        This method maintains compatibility with the old TreeSearchEngine API.

        Args:
            target_molecule: SMILES of target molecule
            max_routes: Maximum number of routes to return
            kwargs: Additional parameters

        Returns:
            List of synthesis routes (each route is a list of ReactionNodes)
        """
        if max_routes is None:
            max_routes = getattr(self.config.processing, 'max_routes', 400)

        logger.info(f"=== Starting synthesis route search for {target_molecule} ===")
        total_start_time = time.time()

        # Run the BFS search
        logger.info("Starting BFS retrosynthesis search...")
        bfs_start_time = time.time()

        all_nodes = self.run_bfs_retrosynthesis(target_molecule)
        bfs_end_time = time.time()
        logger.info(f"BFS retrosynthesis completed in {bfs_end_time - bfs_start_time:.2f} seconds")

        if not all_nodes:
            logger.info("No nodes found in BFS search")
            return []

        # Extract routes for scoring
        logger.info("Extracting routes for scoring...")
        extract_start_time = time.time()
        routes = self.extract_routes_for_scoring(all_nodes)

        extract_end_time = time.time()
        logger.info(f"Route extraction completed in {extract_end_time - extract_start_time:.2f} seconds")

        if not routes:
            logger.info("No routes found for scoring")
            return []

        # Get Parrot predictions for the final routes
        logger.info("Getting Parrot predictions for final routes...")
        parrot_start_time = time.time()
        self._add_parrot_predictions_to_routes(routes, target_molecule)
        parrot_end_time = time.time()
        logger.info(f"Parrot predictions completed in {parrot_end_time - parrot_start_time:.2f} seconds")

        logger.info("Adding rxn class data")
        self._add_rxn_class_data_to_routes(routes)
        # Score and sort routes
        logger.info("Scoring and sorting routes...")
        scoring_start_time = time.time()
        scored_routes = [(route, self.score_route(route)) for route in routes]
        scored_routes.sort(key=lambda x: x[1])  # Sort by score (lower is better)
        scoring_end_time = time.time()
        logger.info(f"Route scoring and sorting completed in {scoring_end_time - scoring_start_time:.2f} seconds")

        total_end_time = time.time()
        total_time = total_end_time - total_start_time

        logger.info(f"=== Synthesis route search completed in {total_time:.2f} seconds ===")
        logger.info(f"  - BFS Search: {bfs_end_time - bfs_start_time:.2f}s ({((bfs_end_time - bfs_start_time)/total_time)*100:.1f}%)")
        logger.info(f"  - Route Extraction: {extract_end_time - extract_start_time:.2f}s ({((extract_end_time - extract_start_time)/total_time)*100:.1f}%)")
        logger.info(f"  - Parrot Predictions: {parrot_end_time - parrot_start_time:.2f}s ({((parrot_end_time - parrot_start_time)/total_time)*100:.1f}%)")
        logger.info(f"  - Route Scoring: {scoring_end_time - scoring_start_time:.2f}s ({((scoring_end_time - scoring_start_time)/total_time)*100:.1f}%)")
        logger.info(f"Found {len(routes)} total routes, returning top {min(max_routes, len(routes))} routes")

        # Return top routes
        return [route for route, _ in scored_routes[:max_routes]]

    def get_reaction_class(self, rxn_string: str) -> dict:
        """Get reaction classification."""
        try:
            resp = requests.post(
                getattr(self.config.api, 'reaction_class_url', ''),
                headers={'Content-Type': 'application/json'},
                json={"smiles": [rxn_string]}
            )
            data = resp.json()
            if data['status'] == 'SUCCESS':
                return data.get('results', [{}])[0]
        except Exception as e:
            logger.warning(f"Failed to get reaction class for {rxn_string}: {e}")
        return {}

    def _add_rxn_class_data_to_routes(self, routes: List[List["ReactionNode"]]):
        """
        Add reaction class data to the final synthesis routes in parallel.

        Args:
            routes: List of synthesis routes (each route is a list of ReactionNodes)
        """

        # Collect all reaction nodes that need classification
        rxn_tasks = []
        for route in routes:
            for reaction in route:
                rxn_string = reaction.reaction_data.get('rxn_string', '')
                if rxn_string:
                    rxn_tasks.append((reaction, rxn_string))
                else:
                    reaction.reaction_data['rxn_class'] = {}

        # Process reactions in parallel with up to 5 threads
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_rxn = {
                executor.submit(self.get_reaction_class, rxn_string): reaction
                for reaction, rxn_string in rxn_tasks
            }

            for future in as_completed(future_to_rxn):
                reaction = future_to_rxn[future]
                try:
                    reaction_class = future.result()
                except Exception as e:
                    logger.error(f"Error classifying reaction: {e}")
                    reaction_class = {}
                reaction.reaction_data['rxn_class'] = reaction_class

        logger.info("Added reaction class data to routes")
        return routes

    def _add_parrot_predictions_to_routes(self, routes: List[List[ReactionNode]], target_molecule: str):
        """
        Add Parrot reagent predictions to the final synthesis routes.

        Args:
            routes: List of synthesis routes (each route is a list of ReactionNodes)
            target_molecule: Target molecule SMILES
        """
        # Extract unique reaction SMILES from all routes
        unique_reactions = set()
        for route in routes:
            for reaction in route:
                retro_smiles = reaction.reaction_data.get('Retro', '')
                if retro_smiles:
                    # Format: precursor>>target
                    rxn_smiles = retro_smiles + '>>' + target_molecule
                    unique_reactions.add(rxn_smiles)

        if not unique_reactions:
            logger.info("No reactions found in routes for Parrot prediction")
            return

        logger.info(f"Calling Parrot for {len(unique_reactions)} unique reactions from final routes")

        # Call Parrot endpoint in batches (with caching)
        try:
            parrot_results = self._call_parrot_api_with_cache(list(unique_reactions), target_molecule)

            if parrot_results:
                # Debug: Log the structure of Parrot results
                logger.info(f"Parrot returned {len(parrot_results)} results")
                if parrot_results:
                    logger.info(f"First Parrot result keys: {list(parrot_results[0].keys())}")
                    logger.info(f"First Parrot result: {parrot_results[0]}")

                # Create mapping from reaction SMILES to Parrot results
                parrot_mapping = {}
                for result in parrot_results:
                    rxn_smiles = result.get('rxn_string', '')
                    if rxn_smiles:
                        parrot_mapping[rxn_smiles] = {
                            'parrot_score': result.get('score', 0.0),
                            'reagents': result.get('reagents', '')
                        }
                        logger.info(f"Mapped {rxn_smiles} -> score: {result.get('score', 0.0)}, reagents: {result.get('reagents', '')}")

                logger.info(f"Created mapping for {len(parrot_mapping)} reactions")

                # Add Parrot data to reaction nodes
                matched_count = 0
                for route in routes:
                    for reaction in route:
                        retro_smiles = reaction.reaction_data.get('Retro', '')
                        if retro_smiles:
                            rxn_smiles = retro_smiles + '>>' + target_molecule
                            if rxn_smiles in parrot_mapping:
                                parrot_data = parrot_mapping[rxn_smiles]
                                reaction.reaction_data['parrot_score'] = parrot_data['parrot_score']
                                reaction.reaction_data['Reagents'] = parrot_data['reagents']
                                matched_count += 1
                                logger.info(f"Matched {rxn_smiles} -> score: {parrot_data['parrot_score']}, reagents: {parrot_data['reagents']}")
                            else:
                                reaction.reaction_data['parrot_score'] = 0.0
                                reaction.reaction_data['Reagents'] = ''
                                logger.info(f"No match for {rxn_smiles}")

                logger.info(f"Matched Parrot data for {matched_count} reactions out of {len(parrot_mapping)} available")

                logger.info(f"Successfully added Parrot predictions to {len(parrot_results)} reactions")
            else:
                logger.info("No Parrot results returned")
        except Exception as e:
            logger.warning(f"Parrot prediction failed for routes: {e}")
            # Add default values to all reactions
            for route in routes:
                for reaction in route:
                    reaction.reaction_data['parrot_score'] = 0.0
                    reaction.reaction_data['Reagents'] = ''

    def _call_parrot_api_with_cache(self, reaction_smiles_list: List[str], target_molecule: str) -> List[Dict]:
        """
        Call Parrot API with caching support.

        Args:
            reaction_smiles_list: List of reaction SMILES strings
            target_molecule: Target molecule SMILES for cache key

        Returns:
            List of Parrot prediction results
        """
        import hashlib
        import json

        # Create cache key from sorted reaction list and target molecule
        cache_key_data = {
            'target_molecule': target_molecule,
            'reactions': sorted(reaction_smiles_list)
        }
        cache_key_str = json.dumps(cache_key_data, sort_keys=True)
        cache_key_hash = hashlib.sha256(cache_key_str.encode()).hexdigest()[:16]

        # Try to get cached results
        try:
            cached_parrot_df = self.disconnections_storage.get_data_from_blob(
                target_smiles=target_molecule,
                analysis_id=f"parrot_{cache_key_hash}",
                data_type="parrot"
            )

            if not cached_parrot_df.empty:
                logger.info(f"Using cached Parrot results for {len(reaction_smiles_list)} reactions")
                # Convert DataFrame back to list of dictionaries
                return cached_parrot_df.to_dict('records')

        except Exception as e:
            logger.warning(f"Failed to load cached Parrot results: {e}")

        # No cache found, call API
        logger.info(f"No cached Parrot results found, calling API for {len(reaction_smiles_list)} reactions")
        parrot_results = self._call_parrot_api(reaction_smiles_list)

        # Cache the results if successful
        if parrot_results:
            try:
                parrot_df = pd.DataFrame(parrot_results)
                self.disconnections_storage.store_data_to_blob(
                    parrot_df,
                    target_molecule,
                    f"parrot_{cache_key_hash}",
                    data_type="parrot"
                )
                logger.info(f"Cached Parrot results for {len(parrot_results)} reactions")
            except Exception as e:
                logger.warning(f"Failed to cache Parrot results: {e}")

        return parrot_results

    def _call_parrot_api(self, reaction_smiles_list: List[str]) -> List[Dict]:
        """
        Call Parrot API in batches for reagent prediction using parallel processing.

        Args:
            reaction_smiles_list: List of reaction SMILES strings

        Returns:
            List of Parrot prediction results
        """
        batch_size = 8
        all_results = []

        parrot_url = getattr(self.config.api, 'parrot_url', '')
        primary_key = getattr(self.config.api, 'parrot_key', '')

        if not parrot_url or not primary_key:
            logger.warning("Parrot URL or key not configured")
            return []

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {primary_key}"
        }

        logger.info(f"Calling Parrot API with parallel processing, URL: {parrot_url}")

        # Create batches
        batches = []
        for start in range(0, len(reaction_smiles_list), batch_size):
            batch = reaction_smiles_list[start:start + batch_size]
            batch_num = start // batch_size + 1
            batches.append((batch, batch_num))

        logger.info(f"Created {len(batches)} batches for parallel processing")

        # Process batches in parallel using ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="Parrot_Worker") as executor:
            # Submit all batch processing tasks
            future_to_batch = {
                executor.submit(self._process_parrot_batch, batch, batch_num, parrot_url, headers): batch_num
                for batch, batch_num in batches
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_num = future_to_batch[future]
                try:
                    batch_results = future.result()
                    if batch_results:
                        all_results.extend(batch_results)
                        logger.debug(f"Parrot batch {batch_num}: completed successfully with {len(batch_results)} results")
                    else:
                        logger.warning(f"Parrot batch {batch_num}: no results returned")
                except Exception as e:
                    logger.warning(f"Parrot batch {batch_num} failed: {e}")

        logger.info(f"Parrot API parallel processing completed. Total results: {len(all_results)}")
        return all_results

    def _process_parrot_batch(self, batch: List[str], batch_num: int, parrot_url: str, headers: Dict[str, str]) -> List[Dict]:
        """
        Process a single batch of reactions for Parrot API prediction.

        Args:
            batch: List of reaction SMILES strings for this batch
            batch_num: Batch number for logging
            parrot_url: Parrot API URL
            headers: HTTP headers for the request

        Returns:
            List of Parrot prediction results for this batch
        """
        parrot_data = {"rxn_smiles": batch}

        logger.debug(f"Parrot batch {batch_num}: sending {len(batch)} reactions")

        try:
            response = guarded_requests_post_with_retry(parrot_url, headers=headers, payload=parrot_data, timeout=self.config.api.requests_timeout)
            result_json = response.json()

            if isinstance(result_json, dict) and 'results' in result_json:
                return result_json['results']
            elif isinstance(result_json, list):
                return result_json
            else:
                logger.warning(f"Parrot batch {batch_num}: unexpected response format")
                return []

        except Exception as e:
            logger.warning(f"Parrot batch {batch_num} failed: {e}")
            return []

    def store_pathways(self, target_smiles: str, routes: List[List[ReactionNode]], kwargs: dict = {}) -> dict:
        """Store synthesis pathways with complete implementation."""
        # Use the same implementation as the original but with updated database service
        import time

        # Create filename with timestamp
        timestamp = int(time.time())
        safe_smiles = target_smiles.replace('/', '_').replace('\\', '_')
        depth = kwargs.get('max_depth', self.max_depth)
        beam_width = kwargs.get('beam_width', getattr(self.config.processing, 'beam_width', 10))

        # Convert routes to serializable format
        pathways_data = {
            'target_smiles': target_smiles,
            'timestamp': timestamp,
            'readable_time': time.ctime(),
            'num_routes': len(routes),
            'routes': []
        }

        smiles_metadata = {}
        all_data = []
        logger.info(f"Storing final {len(routes)} routes for target: {target_smiles} with max depth: {depth} and beam width: {beam_width}")

        for route_idx, route in enumerate(routes):
            route_data = {
                'route_id': route_idx + 1,
                'num_steps': len(route),
                'unique_id': hash(str(route) + str(depth) + str(beam_width)),
                'reactions': []
            }
            raw_smiles = []
            route_level_image = ''
            route_name = ''

            for step_idx, reaction in enumerate(route):
                # Get parent molecule directly from reaction's parent
                parent_molecule = reaction.parent
                parent_molecule_id = parent_molecule.node_id if hasattr(parent_molecule, 'node_id') else None

                # Find the parent reaction by traversing up the tree from the parent molecule
                parent_reaction_id = None
                if parent_molecule and parent_molecule.parent:
                    # The parent reaction is the reaction that produced this molecule
                    parent_reaction = parent_molecule.parent
                    if isinstance(parent_reaction, ReactionNode):
                        parent_reaction_id = parent_reaction.node_id

                reaction_data = {
                    'step': step_idx + 1,
                    'parent_reaction_id': parent_reaction_id,  # ID of reaction that produced the input molecule
                    'parent_molecule_id': parent_molecule_id,  # ID of the input molecule
                    'reaction_id': reaction.node_id,
                    'reaction_string': reaction.reaction_data.get('rxn_string', 'N/A'),
                    'retro_smiles': reaction.reaction_data.get('Retro', ''),
                    'reagents': reaction.reaction_data.get('Reagents', ''),
                    'forward_prediction': reaction.reaction_data.get('Forward_Prediction', ''),
                    'prob_forward_1': reaction.reaction_data.get('Prob_Forward_Prediction_1', 0.0),
                    'prob_forward_2': reaction.reaction_data.get('Prob_Forward_Prediction_2', 0.0),
                    'score': reaction.reaction_score,
                    'rxn_class': reaction.reaction_data.get('rxn_class', {}),
                    'reaction_smiles_img': self.azure_utils.image_to_blob(
                        reaction.reaction_data.get('rxn_string', '')),
                    'other_information': smiles_metadata.get(
                        reaction.reaction_data.get('rxn_string', ''), {}),
                    'reactants': []
                }
                if not route_level_image:
                    route_level_image = reaction_data.get('reaction_smiles_img', '')

                if not route_name and reaction_data:
                    try:
                        route_name = reaction_data.get('rxn_class', {}).get('reaction_name')
                    except:
                        route_name = ''

                mid_materials = []
                for reactant_node in reaction.get_reactant_nodes():
                    reactant_info = {
                        'molecule_id': reactant_node.node_id,
                        'smiles': reactant_node.smiles,
                        'name': '',  # Could add IUPAC name lookup here
                        'synthesis_score': reactant_node.synthesis_score,
                        'is_terminal': reactant_node.status.value if hasattr(reactant_node.status, 'value') else str(reactant_node.status)
                    }
                    reaction_data['reactants'].append(reactant_info)
                    mid_materials.append(reactant_info.get('name'))

                route_data['reactions'].append(reaction_data)
                raw_smiles = mid_materials

            route_data['total_route_score'] = 1 - ((self.score_route(route) - 1) / 4)

            logger.info(f"Storing route {route_idx + 1} with {len(route)} steps, total score: {route_data['total_route_score']}")

            # Store in database
            try:
                route_id = self.db_service.insert_retro_data(
                    target_smiles=target_smiles,
                    request_id=kwargs.get('request_id'),
                    unique_id=route_data['unique_id'],
                    route_id=route_data['route_id'],
                    num_steps=route_data['num_steps'],
                    raw_smiles=raw_smiles,
                    total_cost=0,
                    total_route_score=route_data['total_route_score'],
                    data=route_data['reactions'],
                    route_name=route_name,
                    route_level_image=route_level_image,
                    level=kwargs.get('level', 0),
                    config={
                        'max_depth': kwargs.get('max_depth', self.max_depth),
                        'beam_width': kwargs.get('beam_width', getattr(self.config.processing, 'beam_width', 10))
                    }
                )
                if route_id:
                    logger.info(f"Sending enrich task for route_id: {route_id}")
                    other_info = []
                    for dt in route_data['reactions']:
                        other_info.append({'reaction_smiles': dt.get('reaction_string'), 'reaction_class': json.dumps(dt.get('rxn_class'))})
                    all_data.append({"route_id": route_id, 'other_info': other_info, 'total_score': route_data['total_route_score']})
            except Exception as e:
                logger.error(f"Failed to store route in database: {e}")

            logger.info(f"Stored route {route_data['route_id']} with {route_data['num_steps']} steps and score {route_data['total_route_score']}")
            pathways_data['routes'].append(route_data)

        # Send enrich tasks for top routes
        all_data = sorted(all_data, key=lambda x: x['total_score'], reverse=True)
        for dt in all_data[:25]:
            try:
                send_enrich_inflow_task({"route_id": dt['route_id'], 'other_info': dt['other_info']})
            except Exception as e:
                logger.warning(f"Failed to send enrich task: {e}")

        return pathways_data
