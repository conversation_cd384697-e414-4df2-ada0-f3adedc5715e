"""
Celery application configuration and setup.
"""

import os
import logging
from typing import Dict, Any, Optional

from celery import Celery
from celery.signals import after_setup_logger, worker_process_init

from app.core.config import AppConfig
from app.core.logging import get_logger

logger = get_logger(__name__)


def create_celery_app(config: AppConfig) -> Celery:
    """
    Create and configure Celery application.
    
    Args:
        config: Application configuration
        
    Returns:
        Configured Celery application
    """
    # Setup Redis Sentinel if configured
    sentinel_transport_options = {}
    if config.redis.redis_sentinel_url:
        sentinel_str = config.redis.redis_sentinel_url.replace('/0', '').split("sentinel://")
        sentinel_hosts = [(sentinel_str[1].split(":")[0], int(sentinel_str[1].split(":")[1]))]
        
        sentinel_transport_options = {
            'master_name': config.redis.redis_sentinel_service_name,
            'sentinels': sentinel_hosts,
            'sentinel_kwargs': {
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
            },
            'socket_keepalive': True,
            'retry_on_timeout': True,
            'health_check_interval': 30,
            'visibility_timeout': 21600,  # 6 hours
        }
        broker_url = config.redis.redis_sentinel_url
    else:
        broker_url = config.redis.redis_url
    
    # Create Celery app
    app = Celery('retro_synthesis_worker', broker=broker_url, backend=broker_url)
    
    # Configure Celery
    app.conf.update(
        broker_transport_options=sentinel_transport_options,
        result_backend_transport_options=sentinel_transport_options,
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_disable_rate_limits=True,
        task_reject_on_worker_lost=True,
        task_ignore_result=False,
        worker_max_tasks_per_child=1,
        task_default_queue=config.redis.retro_input_queue,
        task_routes={
            'process_retro_task': {'queue': config.redis.retro_input_queue},
            'process_enrich_inflow_task': {'queue': config.redis.enrich_input_queue},
        },
        # Task serialization
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
    )
    
    # Setup logging for Celery
    @after_setup_logger.connect
    def setup_celery_logger(logger_instance, *args, **kwargs):
        """Setup Celery logger with file handler."""
        os.makedirs('logs', exist_ok=True)
        fh = logging.FileHandler('logs/celery_tasks.log')
        fh.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        ))
        logger_instance.addHandler(fh)
    
    @worker_process_init.connect
    def init_worker(**kwargs):
        """Initialize worker process."""
        logger.info("Celery worker process initialized")
    
    logger.info("Celery application created and configured")
    return app


def get_celery_app(config: Optional[AppConfig] = None) -> Celery:
    """
    Get or create Celery application instance.
    
    Args:
        config: Optional configuration override
        
    Returns:
        Celery application instance
    """
    if config is None:
        from app.core.config import get_config
        config = get_config()
    
    return create_celery_app(config)


# Create the default Celery app instance for the worker
celery_app = get_celery_app()

# Import tasks to register them with Celery
from app.workers import retro_worker, enrich_worker
