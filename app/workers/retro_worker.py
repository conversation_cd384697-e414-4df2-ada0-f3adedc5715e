"""
Retro synthesis worker for processing retro synthesis tasks.
"""

import os
import json
import time
import copy
import uuid
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from celery import Task

from app.core.config import get_config
from app.core.logging import get_logger
from app.core.exceptions import ProcessingError, ValidationError
from app.workers.celery_app import get_celery_app
from app.services.retro_service import RetroSynthesisService
from app.services.database_service import DatabaseService
from app.managers.task_manager import TaskManager
from app.utils.chemistry import REQUEST_ID, canonicalize_smiles

logger = get_logger(__name__)
config = get_config()

# Get Celery app instance
celery_app = get_celery_app()

# Initialize services
db_service = DatabaseService()
retro_service = RetroSynthesisService()
task_manager = TaskManager()

# Thread coordination
thread_lock = threading.Lock()


class RetroWorkerTask(Task):
    """Custom Celery task class for retro synthesis processing."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"Task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success."""
        logger.info(f"Task {task_id} completed successfully")


@celery_app.task(bind=True, base=RetroWorkerTask, max_retries=3, name='process_retro_task')
def process_retro_task(self, payload: Dict[str, Any]) -> bool:
    """
    Process retro synthesis task.
    
    Args:
        payload: Task payload containing request details
        
    Returns:
        True if successful, False otherwise
    """
    start_time = time.time()
    
    try:
        # Validate payload
        _validate_payload(payload)
        
        # Extract and setup request data
        request_id = str(payload["request_id"])
        REQUEST_ID.set(request_id)
        
        molecule_name = payload.get("molecule_name", "")
        target_smiles = canonicalize_smiles(payload["target_smiles"])
        
        # Update environment variables from payload
        _update_environment_from_payload(payload)
        
        logger.info(f"[START] Processing task {request_id} with SMILES: {target_smiles}")
        
        # Check if data is already processed (caching)
        if _is_data_preprocessed(target_smiles, request_id):
            logger.info("[END] Data already processed, using cached results")
            _send_cached_result(request_id, molecule_name)
            return True
        
        # Process parallel configurations
        parallel_configs = _get_parallel_configs()
        
        logger.info(f"[PARALLEL] Starting {len(parallel_configs)} sequential iterations for request {request_id}")
        
        # Process each configuration sequentially
        thread_results = {}
        first_value, last_value = _get_delay_values(parallel_configs)
        
        for idx, config_item in enumerate(parallel_configs):
            logger.info(f"[SEQUENTIAL] Processing iteration {idx} with config: {config_item}")
            
            result = _process_single_iteration(
                config_item, request_id, target_smiles, molecule_name, 
                self, start_time, idx, thread_results, first_value, last_value
            )
            
            thread_results[idx] = result
        
        # Log summary
        completed_iterations = len(parallel_configs)
        successful_iterations = sum(1 for result in thread_results.values() if result)
        failed_iterations = completed_iterations - successful_iterations
        
        logger.info(f"[END] All {completed_iterations} sequential iterations completed for task {request_id}")
        logger.info(f"[SUMMARY] Success: {successful_iterations}, Failed: {failed_iterations}, Total: {completed_iterations}")
        
        return True
        
    except ValidationError as e:
        logger.error(f"Validation error in task {self.request.id}: {e}")
        return False
    except ProcessingError as e:
        logger.error(f"Processing error in task {self.request.id}: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return False
    except Exception as e:
        logger.error(f"Unexpected error in task {self.request.id}: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return False


def _validate_payload(payload: Dict[str, Any]) -> None:
    """Validate task payload."""
    required_fields = {"request_id", "target_smiles"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        raise ValidationError(f"Missing required fields: {missing}")


def _update_environment_from_payload(payload: Dict[str, Any]) -> None:
    """Update environment variables from payload."""
    env_mapping = {
        'SYNTHESIS_SCORE_THRESHOLD': 'synthesis_score_threshold',
        'MIN_FORWARD_PROB': 'min_forward_prob',
        'MIN_CERTAINITY_SCORE': 'min_certainity_score',
        'HEAVY_METAL_THRESHOLD': 'heavy_metal_threshold',
        'PRUNING_FACTOR': 'pruning_factor',
        'BEAM_BASED_PRUNING': 'beam_based_pruning',
        'MAX_ROUTES': 'max_routes'
    }
    
    for env_key, payload_key in env_mapping.items():
        if payload_key in payload:
            os.environ[env_key] = str(payload[payload_key])


def _is_data_preprocessed(target_smiles: str, request_id: str) -> bool:
    """Check if data is already preprocessed (caching logic)."""
    # TODO: Implement caching logic if needed
    return False


def _send_cached_result(request_id: str, molecule_name: str) -> None:
    """Send cached result to output queue."""
    result_data = {
        "request_id": request_id,
        "original_request_id": request_id,
        "molecule_name": molecule_name,
        "status": "SUCCESS",
        "total_routes": 0,
        "processed_at": time.time(),
        "processing_time": 0,
        "cached": True
    }
    
    celery_app.send_task(
        'retro_result_handler',
        args=[result_data],
        queue=config.redis.retro_output_queue
    )


def _get_parallel_configs() -> list:
    """Get parallel configurations."""
    parallel_configs = config.processing.parallel_configs
    if isinstance(parallel_configs, str):
        parallel_configs = json.loads(parallel_configs)
    return parallel_configs


def _get_delay_values(parallel_configs: list) -> tuple:
    """Get first and last delay values from configurations."""
    if not parallel_configs:
        return 0, 0
    
    first_value = parallel_configs[0].get('DELAY', 0)
    last_value = parallel_configs[-1].get('DELAY', 0)
    
    return first_value, last_value


def _process_single_iteration(
    config_item: Dict[str, Any],
    request_id: str,
    target_smiles: str,
    molecule_name: str,
    task_instance: Task,
    start_time: float,
    thread_idx: int,
    results_dict: Dict[int, Any],
    first_value: int,
    last_value: int
) -> bool:
    """Process a single iteration of retro synthesis."""
    thread_request_id = f"{request_id}_{thread_idx}"
    
    try:
        # Extract parameters
        max_depth = int(config_item.get('MAX_DEPTH', 5))
        beam_width = int(config_item.get('BEAM_WIDTH', 3))
        level = int(config_item.get('LEVEL', 0))
        delay = int(config_item.get('DELAY', 0))
        
        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Starting iteration with max_depth={max_depth} and beam_width={beam_width} for request_id {thread_request_id}")
        
        # Update database status for first iteration
        if str(config_item.get('DELAY')) == str(first_value):
            task_manager.update_task_status(request_id, target_smiles, "RUNNING")
        
        # Prepare kwargs for retro synthesis
        kwargs = {
            "max_depth": max_depth,
            "beam_width": beam_width,
            'delay': delay,
            "checkpoint_frequency": int(str((max_depth + 1) * 100)),
            "request_id": request_id,
            "target_smiles": target_smiles,
            'molecule_name': molecule_name,
            "level": level,
        }
        
        # Check if config is already processed
        if task_manager.is_config_processed(request_id, kwargs):
            logger.info(f"[THREAD-{thread_idx}] Config already processed for request_id {thread_request_id}")
            return True
        
        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Processing task {thread_request_id} with SMILES: {target_smiles}")
        
        # Run retro synthesis
        total_routes, message = retro_service.run_retro_synthesis(target_smiles, thread_request_id, kwargs)
        processing_time = time.time() - start_time
        
        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Completed task {thread_request_id} in {processing_time:.2f}s with {total_routes} routes")
        
        # Update database status for last iteration
        if str(config_item.get('DELAY')) == str(last_value):
            task_manager.update_task_status(request_id, target_smiles, "COMPLETED")
        
        # Send result for last iteration
        if str(config_item.get('DELAY')) == str(last_value):
            _send_result(request_id, molecule_name, total_routes, message, processing_time, task_instance, thread_idx, config_item)
        
        # Store result
        if results_dict is not None:
            with thread_lock:
                results_dict[thread_idx] = {
                    "success": True,
                    "routes": total_routes,
                    "processing_time": processing_time,
                    "config": config_item
                }
        
        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Successfully completed processing for {thread_request_id}")
        
        return True
        
    except Exception as e:
        with thread_lock:
            logger.error(f"[THREAD-{thread_idx}] Error in thread {thread_idx} for request {thread_request_id}: {str(e)}")
        
        # Store error result
        if results_dict is not None:
            with thread_lock:
                results_dict[thread_idx] = {
                    "success": False,
                    "error": str(e),
                    "processing_time": time.time() - start_time,
                    "config": config_item
                }
        
        # Handle error
        task_manager.handle_task_error(e, thread_request_id, target_smiles, molecule_name, task_instance, start_time)
        return False


def _send_result(
    request_id: str,
    molecule_name: str,
    total_routes: int,
    message: str,
    processing_time: float,
    task_instance: Task,
    thread_idx: int,
    config_item: Dict[str, Any]
) -> None:
    """Send result to output queue."""
    result_data = {
        "request_id": request_id,
        "original_request_id": request_id,
        "molecule_name": molecule_name,
        "status": "SUCCESS" if message == '' else "UNSOLVABLE",
        "message": message,
        "total_routes": total_routes,
        "processed_at": time.time(),
        "task_id": task_instance.request.id,
        "processing_time": processing_time,
        "thread_index": thread_idx,
        "max_depth": config_item.get('MAX_DEPTH'),
        "beam_width": config_item.get('BEAM_WIDTH')
    }
    
    celery_app.send_task(
        'retro_result_handler',
        args=[result_data],
        queue=config.redis.retro_output_queue
    )
