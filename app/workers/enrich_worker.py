"""
Enrich worker for processing enrichment tasks.
"""

import time
import threading
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from celery import Task

from app.core.config import get_config
from app.core.logging import get_logger
from app.core.exceptions import ProcessingError, ValidationError
from app.workers.celery_app import get_celery_app
from app.services.database_service import DatabaseService
from app.utils.monitoring import get_metrics_collector, time_operation

logger = get_logger(__name__)
config = get_config()

# Get Celery app instance
celery_app = get_celery_app()

# Initialize services
db_service = DatabaseService()
metrics_collector = get_metrics_collector()

# Thread coordination
thread_lock = threading.Lock()


class EnrichWorkerTask(Task):
    """Custom Celery task class for enrichment processing."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"Enrich task {task_id} failed: {exc}")
        logger.error(f"Exception info: {einfo}")
        metrics_collector.increment_counter("enrich_task_failures")
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success."""
        logger.info(f"Enrich task {task_id} completed successfully")
        metrics_collector.increment_counter("enrich_task_successes")


@celery_app.task(bind=True, base=EnrichWorkerTask, max_retries=3, name='process_enrich_inflow_task')
@time_operation("enrich_task_processing")
def process_enrich_inflow_task(self, payload: Dict[str, Any]) -> bool:
    """
    Process enrichment task.
    
    Args:
        payload: Task payload containing enrichment details
        
    Returns:
        True if successful, False otherwise
    """
    start_time = time.time()
    
    try:
        
        # Extract request data
        request_id = str(payload.get("request_id"))
        reaction_data = payload.get("reaction_data", [])
        
        logger.info(f"[ENRICH] Processing enrichment task {request_id} with {len(reaction_data)} reactions")
        
        # Store enriched data
        route_id = str(payload["route_id"])
        enrich_data(route_id, payload.get('other_info'))
                
        processing_time = time.time() - start_time
        logger.info(f"[ENRICH] Completed enrichment task {request_id} in {processing_time:.2f}s")
        
        return True
        
    except ValidationError as e:
        logger.error(f"Validation error in enrich task {self.request.id}: {e}")
        return False
    except ProcessingError as e:
        logger.error(f"Processing error in enrich task {self.request.id}: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return False
    except Exception as e:
        logger.error(f"Unexpected error in enrich task {self.request.id}: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return False


def _validate_enrich_payload(payload: Dict[str, Any]) -> None:
    """Validate enrichment task payload."""
    required_fields = {"request_id"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        raise ValidationError(f"Missing required fields: {missing}")

def enrich_data(route_id, metadata):
    """Enriches the top 100 routes with additional reaction data."""

    top_routes = db_service.get_route_by_id(route_id)

    if not top_routes:
        return

    reaction_class = {}
    for reaction_data in metadata:
        reaction_class[reaction_data.get('reaction_smiles')] = reaction_data.get('other_info')

    for route in top_routes:
        for step in route.get('data', []):
            reaction_string = step.get('reaction_string')
            if reaction_string:
                step['other_information'] = reaction_class.get(reaction_string, {})

    try:
        db_service.update_retro_data(route_id, route['data'])
        logger.info(f"[CRON Enrich] Updated route {route_id} in database")
    except Exception as e:
        logger.error(f"[CRON Enrich] Failed to update route {route_id}: {e}")


def _send_enrich_result(request_id: str, enriched_data: list, start_time: float) -> None:
    """
    Send enrichment result to output queue.
    
    Args:
        request_id: Request identifier
        enriched_data: Enriched reaction data
        start_time: Task start time
    """
    try:
        result_data = {
            "request_id": request_id,
            "status": "SUCCESS",
            "enriched_reactions_count": len(enriched_data),
            "processed_at": time.time(),
            "processing_time": time.time() - start_time,
            "enriched_data": enriched_data
        }
        
        celery_app.send_task(
            'enrich_result_handler',
            args=[result_data],
            queue=config.redis.enrich_output_queue
        )
        
        logger.debug(f"Sent enrichment result for request {request_id}")
        
    except Exception as e:
        logger.error(f"Failed to send enrichment result for {request_id}: {e}")


# Helper function for sending enrichment tasks (used by other modules)
def send_enrich_inflow_task(reaction_data: dict) -> None:
    """
    Send enrichment task to the queue.
    
    Args:
        request_id: Request identifier
        reaction_data: Reaction data to enrich
    """
    try:
        payload = reaction_data
        
        celery_app.send_task(
            'process_enrich_task',
            args=[payload],
            queue=config.redis.enrich_input_queue
        )
        
        logger.info(f"Sent enrichment task for request with {len(reaction_data)} reactions")
        
    except Exception as e:
        logger.error(f"Failed to send enrichment task for: {e}")
        raise ProcessingError(f"Failed to send enrichment task: {e}")
