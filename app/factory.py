"""
Application factory for creating and configuring the application.
"""

import os
import time
from typing import Optional

from app.core.config import get_config
from app.core.logging import get_logger
from app.workers.celery_app import create_celery_app
from app.handlers.health_check import HealthCheckServer
from app.utils.monitoring import get_metrics_collector

logger = get_logger(__name__)


class Application:
    """Main application class that coordinates all components."""
    
    def __init__(self, config=None):
        """
        Initialize the application.

        Args:
            config: Optional configuration override
        """
        self.config = config or get_config()
        self.celery_app = None
        self.health_server = None
        self.metrics_collector = get_metrics_collector()

        logger.info("Application initialized with configuration")
        logger.info(f"Database: {self.config.database.mongo_url}")
        logger.info(f"Redis: {self.config.redis.redis_url}")
        logger.info(f"Use Celery: {self.config.use_celery}")
    
    def setup_celery(self):
        """Setup Celery application."""
        if self.config.use_celery:
            self.celery_app = create_celery_app(self.config)
            logger.info("Celery application configured")
        else:
            logger.info("Celery disabled in configuration")
    
    def setup_health_check(self, port: int = 8030):
        """
        Setup health check server.
        
        Args:
            port: Port for health check server
        """
        self.health_server = HealthCheckServer(port=port)
        logger.info(f"Health check server configured on port {port}")
    
    def start(self):
        """Start the application components."""
        logger.info("Starting application components...")

        if self.health_server:
            self.health_server.start()

        # Log initial metrics
        self.metrics_collector.set_gauge("application_start_time", time.time())

        logger.info("Application started successfully")
        logger.info("Health check available at http://localhost:8030/health")

    def stop(self):
        """Stop the application components."""
        logger.info("Stopping application components...")

        if self.health_server:
            self.health_server.stop()

        # Log final metrics summary
        logger.info("Final metrics summary:")
        logger.info(self.metrics_collector.get_summary())

        logger.info("Application stopped")

    def get_status(self) -> dict:
        """
        Get application status.

        Returns:
            Dictionary with application status information
        """
        return {
            "status": "running" if self.health_server and self.health_server.is_running() else "stopped",
            "config": {
                "use_celery": self.config.use_celery,
                "database": self.config.database.database_name,
                "redis_queues": {
                    "retro_input": self.config.redis.retro_input_queue,
                    "retro_output": self.config.redis.retro_output_queue,
                    "enrich_input": self.config.redis.enrich_input_queue,
                    "enrich_output": self.config.redis.enrich_output_queue,
                }
            },
            "metrics": self.metrics_collector.get_metrics()
        }


def create_app(config=None) -> Application:
    """
    Create and configure the application.
    
    Args:
        config: Optional configuration override
        
    Returns:
        Configured Application instance
    """
    app = Application(config)
    
    # Setup components
    app.setup_celery()
    app.setup_health_check()
    
    return app
