from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid

class NodeType(Enum):
    OR = "OR"    # Molecule node - can be solved by any of its reactions
    AND = "AND"  # Reaction node - requires all precursors to be solved

class NodeStatus(Enum):
    UNSOLVED = "UNSOLVED"
    SOLVED = "SOLVED"
    TERMINAL = "TERMINAL"
    INVALID = "INVALID"

@dataclass
class TreeNode(ABC):
    """Base class for tree nodes."""
    node_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    depth: int = 0
    status: NodeStatus = NodeStatus.UNSOLVED
    parent: Optional['TreeNode'] = None
    children: List['TreeNode'] = field(default_factory=list)
    score: float = 0.0
    
    def add_child(self, child: 'TreeNode') -> None:
        """Add a child node."""
        child.parent = self
        #child.depth = self.depth + 1
        #only increment depth for molecule nodes
        if isinstance(child, MoleculeNode): #and isinstance(self, ReactionNode):
            child.depth = self.depth + 1  # Only molecule children of reactions get deeper
        else:
            child.depth = self.depth  # Reaction children stay at same depth as parent molecule        
        self.children.append(child)
    
    @abstractmethod
    def is_solved(self) -> bool:
        """Check if the node is solved."""
        pass

@dataclass
class MoleculeNode(TreeNode):
    """OR node representing a molecule."""
    smiles: str = ""
    synthesis_score: Optional[float] = None
    
    def __post_init__(self):
        self.node_type = NodeType.OR
    
    def is_solved(self) -> bool:
        """A molecule is solved if it's terminal or any of its reactions is solved."""
        if self.status == NodeStatus.TERMINAL:
            return True
        return any(child.is_solved() for child in self.children if isinstance(child, ReactionNode))
    
    def get_solved_reactions(self) -> List['ReactionNode']:
        """Get all solved reaction children."""
        return [child for child in self.children 
                if isinstance(child, ReactionNode) and child.is_solved()]

@dataclass
class ReactionNode(TreeNode):
    """AND node representing a reaction."""
    reaction_data: Dict[str, Any] = field(default_factory=dict)
    reactants: List[str] = field(default_factory=list)
    reaction_score: Optional[float] = None
    
    def __post_init__(self):
        self.node_type = NodeType.AND
    
    def is_solved(self) -> bool:
        """A reaction is solved if all its precursor molecules are solved."""
        if self.status == NodeStatus.INVALID:
            return False
        return all(child.is_solved() for child in self.children if isinstance(child, MoleculeNode))
    
    def get_reactant_nodes(self) -> List[MoleculeNode]:
        """Get all reactant molecule nodes."""
        return [child for child in self.children if isinstance(child, MoleculeNode)]