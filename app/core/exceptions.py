"""
Custom exception classes for the application.
"""

from typing import Optional, Dict, Any


class RetroSynthesisError(Exception):
    """Base exception for retro synthesis related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ConfigurationError(RetroSynthesisError):
    """Raised when there's a configuration error."""
    pass


class DatabaseError(RetroSynthesisError):
    """Raised when there's a database operation error."""
    pass


class APIError(RetroSynthesisError):
    """Raised when there's an external API error."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)
        self.status_code = status_code


class ProcessingError(RetroSynthesisError):
    """Raised when there's a processing error."""
    pass


class ValidationError(RetroSynthesisError):
    """Raised when there's a validation error."""
    pass


class WorkerError(RetroSynthesisError):
    """Raised when there's a worker-related error."""
    pass


class TimeoutError(RetroSynthesisError):
    """Raised when an operation times out."""
    pass
