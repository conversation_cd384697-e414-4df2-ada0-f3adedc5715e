"""
Enhanced configuration management system.
"""

import os
import json
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    mongo_url: str = field(default_factory=lambda: os.getenv('MONGO_URL', '**************************************'))
    database_name: str = field(default_factory=lambda: os.getenv('DATABASE_NAME', 'retro_synthesis'))


@dataclass
class RedisConfig:
    """Redis and Celery configuration settings."""
    redis_url: str = field(default_factory=lambda: os.getenv("REDIS_URL", "redis://localhost:6379/0"))
    redis_sentinel_url: Optional[str] = field(default_factory=lambda: os.getenv('REDIS_SENTINEL_URL'))
    redis_sentinel_service_name: str = field(default_factory=lambda: os.getenv('REDIS_SENTINEL_SERVICE_NAME', 'mymaster'))
    
    # Queue names
    retro_input_queue: str = field(default_factory=lambda: os.getenv('RETRO_INPUT_REDIS_QUEUE', 'retro_input_job_queue'))
    retro_output_queue: str = field(default_factory=lambda: os.getenv('RETRO_OUTPUT_REDIS_QUEUE', 'retro_output_job_queue'))
    enrich_input_queue: str = field(default_factory=lambda: os.getenv('ENRICH_INPUT_REDIS_QUEUE', 'enrich_input_job_queue'))
    enrich_output_queue: str = field(default_factory=lambda: os.getenv('ENRICH_OUTPUT_REDIS_QUEUE', 'enrich_output_job_queue'))


@dataclass
class APIConfig:
    """External API configuration settings."""
    azure_hostname: str = field(default_factory=lambda: os.getenv('AZURE_HOSTNAME', "***********"))
    
    agent_hub_endpoint: str = field(default_factory=lambda: os.getenv('AGENT_HUB_ENDPOINT', 'localhost:8000'))
    # API URLs
    askos_predictions_pistacho_url: str = field(init=False)
    askcos_em_url: str = field(init=False)
    reaction_class_url: str = field(init=False)
    askos_pre_reaxys: str = field(init=False)
    
    # Azure ML endpoints
    azure_disconnection_tagger_url: str = field(default_factory=lambda: os.getenv('AZURE_DISCONNECTION_TAGGER_URL', 
        "https://disconnection-tagger-v1.eastus2.inference.ml.azure.com/score"))
    azure_reactant_generator_url: str = field(default_factory=lambda: os.getenv('AZURE_REACTANT_GENERATOR_URL',
        "https://reactant-generator-v1.eastus2.inference.ml.azure.com/score"))
    azure_retro_ranker_url: str = field(default_factory=lambda: os.getenv('AZURE_RETRO_RANKER_URL',
        "https://retro-ranker-v1.eastus2.inference.ml.azure.com/score"))
    parrot_url: str = field(default_factory=lambda: os.getenv('PARROT_URL',
        "https://parrot-v1.eastus2.inference.ml.azure.com/score"))
    
    # API Keys
    parrot_key: Optional[str] = field(default_factory=lambda: os.getenv('PARROT_KEY'))
    azure_reactant_generator_api_key: Optional[str] = field(default_factory=lambda: os.getenv('AZURE_REACTANT_GENERATOR_API_KEY'))
    azure_retro_ranker_api_key: Optional[str] = field(default_factory=lambda: os.getenv('AZURE_RETRO_RANKER_API_KEY'))
    azure_disconnection_tagger_api_key: Optional[str] = field(default_factory=lambda: os.getenv('AZURE_DISCONNECTION_TAGGER_API_KEY'))

    azure_blob_connection_string: Optional[str] = field(default_factory=lambda: os.getenv('AZURE_BLOB_CONNECTION_STRING'))
    azure_blob_container_name: str = field(default_factory=lambda: os.getenv('AZURE_BLOB_CONTAINER_NAME', 'imagestorage'))
    
    # Request settings
    requests_timeout: int = field(default_factory=lambda: int(os.getenv('REQUESTS_TIMEOUT', '120')))
    
    def __post_init__(self):
        """Initialize computed fields."""
        self.askos_predictions_pistacho_url = f"http://{self.azure_hostname}:9420/predictions/pistachio_23Q3"
        self.askcos_em_url = f"http://{self.azure_hostname}:9451/predictions"
        self.reaction_class_url = f"http://{self.azure_hostname}:9621/reaction_class"
        self.askos_pre_reaxys = f"http://{self.azure_hostname}:9410/predictions/reaxys"


@dataclass
class ProcessingConfig:
    """Processing and algorithm configuration settings."""
    # Tree search parameters
    max_depth: int = field(default_factory=lambda: int(os.getenv('MAX_DEPTH', '3')))
    beam_width: int = field(default_factory=lambda: int(os.getenv('BEAM_WIDTH', '10')))
    max_routes: int = field(default_factory=lambda: int(os.getenv('MAX_ROUTES', '100')))
    
    # Scoring thresholds
    synthesis_score_threshold: float = field(default_factory=lambda: float(os.getenv('SYNTHESIS_SCORE_THRESHOLD', '1.05')))
    min_forward_prob: float = field(default_factory=lambda: float(os.getenv('MIN_FORWARD_PROB', '0.7')))
    min_certainty_score: float = field(default_factory=lambda: float(os.getenv('MIN_CERTAINITY_SCORE', '0.7')))
    heavy_metal_threshold: float = field(default_factory=lambda: float(os.getenv('HEAVY_METAL_THRESHOLD', '31.0')))
    scscore_threshold: float = field(default_factory=lambda: float(os.getenv('SCSCORE_THRESHOLD', '1.25')))
    
    # Pruning parameters
    pruning_factor: float = field(default_factory=lambda: float(os.getenv('PRUNING_FACTOR', '0.05')))
    beam_based_pruning: int = field(default_factory=lambda: int(os.getenv('BEAM_BASED_PRUNING', '1')))
    
    # Worker settings
    parallel_bfs_workers: int = field(default_factory=lambda: int(os.getenv('PARALLEL_BFS_WORKERS', '6')))
    reactant_generator_batch_size: int = field(default_factory=lambda: int(os.getenv('REACTANT_GENERATOR_BATCH_SIZE', '64')))
    
    # Parallel configurations
    parallel_configs: List[Dict[str, Any]] = field(default_factory=lambda: json.loads(
        os.getenv('PARALLEL_CONFIGS', '''[
            {"MAX_DEPTH": 1, "BEAM_WIDTH": 100, "DELAY": 1, "LEVEL": 1},
            {"MAX_DEPTH": 2, "BEAM_WIDTH": 10, "DELAY": 2, "LEVEL": 2},
            {"MAX_DEPTH": 3, "BEAM_WIDTH": 20, "DELAY": 3, "LEVEL": 3},
            {"MAX_DEPTH": 5, "BEAM_WIDTH": 30, "DELAY": 4, "LEVEL": 4}
        ]''')
    ))
    
    # Custom mappings
    custom_mappings: str = field(default_factory=lambda: os.getenv('custom_mapping', ''))
    
    # Checkpointing
    checkpoint_frequency: int = field(init=False)
    
    def __post_init__(self):
        """Initialize computed fields."""
        self.checkpoint_frequency = int(os.getenv('CHECKPOINT_FREQUENCY', str((self.max_depth + 1) * 100)))


@dataclass
class AzureConfig:
    """Azure storage configuration settings."""
    blob_connection_string: str = field(default_factory=lambda: os.getenv('AZURE_BLOB_CONNECTION_STRING', ''))
    blob_container_name: str = field(default_factory=lambda: os.getenv('AZURE_BLOB_CONTAINER_NAME', 'imagestorage'))
    blob_disconnections_base_path: str = field(default_factory=lambda: os.getenv('AZURE_BLOB_DISCONNECTIONS_BASE_PATH', 'molecules/'))


@dataclass
class AppConfig:
    """Main application configuration."""
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    redis: RedisConfig = field(default_factory=RedisConfig)
    api: APIConfig = field(default_factory=APIConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    azure: AzureConfig = field(default_factory=AzureConfig)
    
    # Application settings
    use_celery: bool = field(default_factory=lambda: os.getenv('USE_CELERY', 'False').lower() == 'true')
    single_step_config: str = field(default_factory=lambda: os.getenv('SINGLE_STEP_CONFIG', 'config/singlestep_config.yaml'))
    building_blocks_path: str = field(default_factory=lambda: os.getenv('BUILDING_BLOCKS_PATH', 'assets/stocks/mstack_buildingblocks.json'))
    pre_loaded_retro_models: int = field(default_factory=lambda: int(os.getenv('PRE_LOADED_RETRO_MODELS', '0')))
    
    # Threading
    api_semaphore: threading.Semaphore = field(default_factory=lambda: threading.Semaphore(6))
    cache_lock: threading.Lock = field(default_factory=threading.Lock)
    
    def validate(self) -> None:
        """Validate configuration settings."""
        if self.processing.max_depth <= 0:
            raise ValueError("MAX_DEPTH must be positive")
        if self.processing.beam_width <= 0:
            raise ValueError("BEAM_WIDTH must be positive")
        if not self.database.mongo_url:
            raise ValueError("MONGO_URL is required")
        if not self.redis.redis_url:
            if not self.redis.redis_sentinel_url:
                raise ValueError("REDIS_URL or REDIS_SENTINEL_URL is required")


# Global configuration instance
_config_instance: Optional[AppConfig] = None
_config_lock = threading.Lock()


def get_config() -> AppConfig:
    """
    Get the global configuration instance (singleton pattern).
    
    Returns:
        AppConfig instance
    """
    global _config_instance
    
    if _config_instance is None:
        with _config_lock:
            if _config_instance is None:
                _config_instance = AppConfig()
                _config_instance.validate()
    
    return _config_instance


def reload_config() -> AppConfig:
    """
    Reload the configuration (useful for testing).
    
    Returns:
        New AppConfig instance
    """
    global _config_instance
    
    with _config_lock:
        _config_instance = None
        return get_config()
