"""
Chemistry-related utility functions.
"""

import contextvars
from typing import Optional, List
from rdkit import Chem

from app.core.logging import get_logger
from app.core.exceptions import ValidationError

logger = get_logger(__name__)

# Context variable for request tracking
REQUEST_ID = contextvars.ContextVar('REQUEST_ID', default=None)


def neutralize_smiles(smiles: str) -> str:
    """
    Neutralize charged molecules in SMILES string.
    
    Args:
        smiles: Input SMILES string
        
    Returns:
        Neutralized SMILES string
        
    Raises:
        ValidationError: If SMILES processing fails
    """
    if '-' not in smiles and '+' not in smiles:
        return smiles
    
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            raise ValidationError(f"Invalid SMILES: {smiles}")
        
        pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
        at_matches = mol.GetSubstructMatches(pattern)
        at_matches_list = [y[0] for y in at_matches]
        
        if len(at_matches_list) > 0:
            for at_idx in at_matches_list:
                atom = mol.GetAtomWithIdx(at_idx)
                chg = atom.GetFormalCharge()
                hcount = atom.GetTotalNumHs()
                atom.SetFormalCharge(0)
                atom.SetNumExplicitHs(hcount - chg)
                atom.UpdatePropertyCache()
        
        return Chem.MolToSmiles(mol)
        
    except Exception as e:
        logger.warning(f"Failed to neutralize SMILES {smiles}: {e}")
        return smiles


def canonicalize_smiles(smiles: str) -> str:
    """
    Canonicalize SMILES string while preserving molecule order.
    
    Args:
        smiles: Input SMILES string
        
    Returns:
        Canonicalized SMILES string
        
    Raises:
        ValidationError: If SMILES canonicalization fails
    """
    try:
        returned = []
        any_error = False
        
        for molecule in smiles.split('.'):
            molecule = neutralize_smiles(molecule)
            mol = Chem.MolFromSmiles(molecule)
            
            if mol is not None:
                canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True)
                returned.append(canonical_smiles)
            else:
                any_error = True
                logger.warning(f"Failed to parse molecule: {molecule}")
        
        if not any_error and returned:
            result = '.'.join(returned)
            logger.debug(f"Canonicalized {smiles} -> {result}")
            return result
        else:
            raise ValidationError(f"Failed to canonicalize SMILES: {smiles}")
            
    except Exception as e:
        logger.error(f"SMILES canonicalization error for {smiles}: {e}")
        raise ValidationError(f"SMILES canonicalization failed: {e}")


def validate_smiles(smiles: str) -> bool:
    """
    Validate SMILES string.
    
    Args:
        smiles: SMILES string to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        return mol is not None
    except Exception as e:
        logger.debug(f"SMILES validation failed for {smiles}: {e}")
        return False


def get_molecular_weight(smiles: str) -> Optional[float]:
    """
    Calculate molecular weight from SMILES.
    
    Args:
        smiles: SMILES string
        
    Returns:
        Molecular weight or None if calculation fails
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            from rdkit.Chem import Descriptors
            return Descriptors.MolWt(mol)
        return None
    except Exception as e:
        logger.debug(f"Molecular weight calculation failed for {smiles}: {e}")
        return None


def get_heavy_atom_count(smiles: str) -> Optional[int]:
    """
    Get heavy atom count from SMILES.
    
    Args:
        smiles: SMILES string
        
    Returns:
        Heavy atom count or None if calculation fails
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            return mol.GetNumHeavyAtoms()
        return None
    except Exception as e:
        logger.debug(f"Heavy atom count calculation failed for {smiles}: {e}")
        return None


def split_reaction_smiles(reaction_smiles: str) -> tuple:
    """
    Split reaction SMILES into reactants, agents, and products.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        Tuple of (reactants, agents, products) as lists
    """
    try:
        if '>>' not in reaction_smiles:
            raise ValidationError(f"Invalid reaction SMILES: {reaction_smiles}")
        
        parts = reaction_smiles.split('>>')
        if len(parts) != 2:
            raise ValidationError(f"Invalid reaction SMILES format: {reaction_smiles}")
        
        left_side = parts[0]
        products = parts[1].split('.') if parts[1] else []
        
        if '>' in left_side:
            reactant_part, agent_part = left_side.split('>', 1)
            reactants = reactant_part.split('.') if reactant_part else []
            agents = agent_part.split('.') if agent_part else []
        else:
            reactants = left_side.split('.') if left_side else []
            agents = []
        
        # Clean empty strings
        reactants = [r.strip() for r in reactants if r.strip()]
        agents = [a.strip() for a in agents if a.strip()]
        products = [p.strip() for p in products if p.strip()]
        
        return reactants, agents, products
        
    except Exception as e:
        logger.error(f"Failed to split reaction SMILES {reaction_smiles}: {e}")
        raise ValidationError(f"Failed to parse reaction SMILES: {e}")


def is_valid_reaction_smiles(reaction_smiles: str) -> bool:
    """
    Validate reaction SMILES string.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        True if valid, False otherwise
    """
    try:
        reactants, agents, products = split_reaction_smiles(reaction_smiles)
        
        # Check that we have at least one reactant and one product
        if not reactants or not products:
            return False
        
        # Validate all SMILES components
        all_smiles = reactants + agents + products
        return all(validate_smiles(smiles) for smiles in all_smiles)
        
    except Exception:
        return False


def get_custom_mappings(custom_mappings_str: str) -> dict:
    """
    Parse custom mappings from string.
    
    Args:
        custom_mappings_str: JSON string of custom mappings
        
    Returns:
        Dictionary of custom mappings
    """
    try:
        if not custom_mappings_str:
            return {}
        
        import json
        return json.loads(custom_mappings_str)
        
    except Exception as e:
        logger.warning(f"Failed to parse custom mappings: {e}")
        return {}
