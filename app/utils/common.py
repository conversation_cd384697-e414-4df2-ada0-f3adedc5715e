"""
Common utility functions.
Moved from utils/common.py with enhancements.
"""

import requests
import json
import os
import time
import threading
from typing import Dict, Any, Optional

from app.core.logging import get_logger
from app.core.config import get_config

logger = get_logger(__name__)

agent_hub_endpoint = get_config().api.agent_hub_endpoint

# Static response for testing/fallback
static_response = {
    "reaction_smiles_interpreted": "O=C(O)c1ccncc1C(F)(F)F.ClS(=O)Cl.ClS(=O)Cl>>O=C(Cl)c1ccncc1C(F)(F)F.O=S.Cl",
    "reaction_details": {
        "reactants_identified": [
            "3-(trifluoromethyl)pyridine-4-carboxylic acid",
            "thionyl dichloride",
            "thionyl dichloride"
        ],
        "products_identified": [
            "3-(trifluoromethyl)pyridine-4-carbonyl chloride",
            "sulfur monoxide",
            "molecular chlorine"
        ],
        "reaction_name": "Carboxylic acid to acid chloride"
    },
    "reagents_and_solvents": [
        {
            "name": "thionyl dichloride",
            "role": "Reagent/Catalyst (LLM Predicted)",
            "price_per_unit": 1100.0,
            "currency": "INR",
            "unit_basis": "kg",
            "price_source": "Secondary Local Data (Local Cache)",
            "price_confidence": "High"
        },
        {
            "name": "dichloromethane",
            "role": "Solvent (LLM Predicted)",
            "price_per_unit": 37.0,
            "currency": "INR",
            "unit_basis": "kg",
            "price_source": "Primary Local Data (Local Cache)",
            "price_confidence": "High"
        }
    ]
}


def guarded_requests_post_with_retry(url: str, headers: dict, payload: dict, max_retries: int = 4, timeout: int = 30):
    """
    Make a POST request with retry logic, rate limiting, and error handling.

    Args:
        url: Request URL
        headers: Request headers
        payload: Request payload
        max_retries: Maximum number of retries

    Returns:
        Response object

    Raises:
        Exception: If all retries fail
    """
    config = get_config()
    thread_name = threading.current_thread().name
    logger.debug(f"[{thread_name}] waiting for semaphore for {url.split('/')[-2]}...")

    with config.api_semaphore:
        logger.debug(f"[{thread_name}] ACQUIRED semaphore. Making API call.")
        retries = 0
        while retries < max_retries:
            try:
                response = requests.post(url, headers=headers, json=payload, timeout=timeout)
                if response.status_code == 200:
                    logger.debug(f"[{thread_name}] API call successful.")
                    return response
                elif response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 15))
                    logger.warning(f"[{thread_name}] Received 429 (Too Many Requests). Waiting {retry_after}s.")
                    time.sleep(retry_after + 1)
                elif response.status_code in [408, 500, 502, 503, 504]:
                    wait_time = 2 ** (retries + 1)
                    logger.warning(f"[{thread_name}] Server error {response.status_code}. Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"[{thread_name}] Client error {response.status_code}: {response.text}. Not retrying.")
                    response.raise_for_status()
            except requests.exceptions.RequestException as e:
                logger.warning(f"[{thread_name}] Network-level error: {e}. Retrying...")
                time.sleep(2 ** (retries + 1))
            retries += 1
    raise Exception(f"API call to {url} failed after {max_retries} retries.")

def smiles_to_iupac_converter(smiles: str) -> str:
    """
    Convert SMILES to IUPAC name using PubChem API via agent hub.

    Args:
        smiles: SMILES string to convert

    Returns:
        IUPAC name or empty string if conversion fails
    """
    retries = 3
    backoff = 3

    for attempt in range(1, retries + 1):
        try:
            url = f"{agent_hub_endpoint}/api/v1/chemcopilot/chemical_converter"
            data = {
                "identifier": smiles,
            }
            response = requests.post(url, data=json.dumps(data), headers={"Content-Type": "application/json"}, timeout=30)
            if response.ok:
                data = response.json()
                iupac_name = data.get('iupac_name')
                if not iupac_name:
                    raise Exception("No IUPAC name found in response")
                return iupac_name
            else:
                logger.warning(f"Attempt {attempt} failed: HTTP {response.status_code}")
        except Exception as e:
            logger.warning(f"Attempt {attempt} failed: {str(e)}")

        if attempt < retries:
            time.sleep(backoff)

    return ''

