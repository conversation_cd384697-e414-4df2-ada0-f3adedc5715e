"""
Common utility functions.
Moved from utils/common.py with enhancements.
"""

import requests
import json
import os
import time
import threading
from typing import Dict, Any, Optional

from app.core.logging import get_logger
from app.core.config import get_config

logger = get_logger(__name__)

agent_hub_endpoint = get_config().api.agent_hub_endpoint

# Static response for testing/fallback
static_response = {
    "reaction_smiles_interpreted": "O=C(O)c1ccncc1C(F)(F)F.ClS(=O)Cl.ClS(=O)Cl>>O=C(Cl)c1ccncc1C(F)(F)F.O=S.Cl",
    "reaction_details": {
        "reactants_identified": [
            "3-(trifluoromethyl)pyridine-4-carboxylic acid",
            "thionyl dichloride",
            "thionyl dichloride"
        ],
        "products_identified": [
            "3-(trifluoromethyl)pyridine-4-carbonyl chloride",
            "sulfur monoxide",
            "molecular chlorine"
        ],
        "reaction_name": "Carboxylic acid to acid chloride"
    },
    "reagents_and_solvents": [
        {
            "name": "thionyl dichloride",
            "role": "Reagent/Catalyst (LLM Predicted)",
            "price_per_unit": 1100.0,
            "currency": "INR",
            "unit_basis": "kg",
            "price_source": "Secondary Local Data (Local Cache)",
            "price_confidence": "High"
        },
        {
            "name": "dichloromethane",
            "role": "Solvent (LLM Predicted)",
            "price_per_unit": 37.0,
            "currency": "INR",
            "unit_basis": "kg",
            "price_source": "Primary Local Data (Local Cache)",
            "price_confidence": "High"
        }
    ]
}


def guarded_requests_post_with_retry(url: str, headers: dict, payload: dict, max_retries: int = 4, timeout: int = 30):
    """
    Make a POST request with retry logic, rate limiting, and error handling.

    Args:
        url: Request URL
        headers: Request headers
        payload: Request payload
        max_retries: Maximum number of retries

    Returns:
        Response object

    Raises:
        Exception: If all retries fail
    """
    config = get_config()
    thread_name = threading.current_thread().name
    logger.debug(f"[{thread_name}] waiting for semaphore for {url.split('/')[-2]}...")

    with config.api_semaphore:
        logger.debug(f"[{thread_name}] ACQUIRED semaphore. Making API call.")
        retries = 0
        while retries < max_retries:
            try:
                response = requests.post(url, headers=headers, json=payload, timeout=timeout)
                if response.status_code == 200:
                    logger.debug(f"[{thread_name}] API call successful.")
                    return response
                elif response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 15))
                    logger.warning(f"[{thread_name}] Received 429 (Too Many Requests). Waiting {retry_after}s.")
                    time.sleep(retry_after + 1)
                elif response.status_code in [408, 500, 502, 503, 504]:
                    wait_time = 2 ** (retries + 1)
                    logger.warning(f"[{thread_name}] Server error {response.status_code}. Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"[{thread_name}] Client error {response.status_code}: {response.text}. Not retrying.")
                    response.raise_for_status()
            except requests.exceptions.RequestException as e:
                logger.warning(f"[{thread_name}] Network-level error: {e}. Retrying...")
                time.sleep(2 ** (retries + 1))
            retries += 1
    raise Exception(f"API call to {url} failed after {max_retries} retries.")


def get_reaction_smiles_data(reaction_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get enriched reaction SMILES data.
    
    Args:
        reaction_data: Reaction data dictionary
        
    Returns:
        Enriched reaction data
    """
    try:
        # Extract reaction SMILES if available
        reaction_smiles = reaction_data.get('rxn_string', '')
        
        if not reaction_smiles:
            logger.warning("No reaction SMILES found in reaction data")
            return reaction_data
        
        # Try to get enriched data from API
        try:
            enriched_data = call_api_hub_for_reaction_enrichment(reaction_smiles)
            if enriched_data:
                return {**reaction_data, **enriched_data}
        except Exception as e:
            logger.warning(f"Failed to enrich reaction data via API: {e}")
        
        # Return original data if enrichment fails
        return reaction_data
        
    except Exception as e:
        logger.error(f"Error processing reaction data: {e}")
        return reaction_data


def call_api_hub_for_reaction_enrichment(reaction_smiles: str) -> Optional[Dict[str, Any]]:
    """
    Call API hub for reaction enrichment.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        Enriched reaction data or None if failed
    """
    try:
        url = f"{agent_hub_endpoint}/api/v1/chemcopilot/chemical_converter"
        headers = {"Content-Type": "application/json"}
        payload = {"reaction_smiles": reaction_smiles}
        
        response = guarded_requests_post_with_retry(url, headers, payload)
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"API hub returned status {response.status_code}")
            return None
            
    except Exception as e:
        logger.warning(f"Failed to call API hub for reaction enrichment: {e}")
        return None


def format_reaction_data_for_storage(reaction_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format reaction data for database storage.
    
    Args:
        reaction_data: Raw reaction data
        
    Returns:
        Formatted reaction data
    """
    try:
        formatted_data = {
            "reaction_smiles": reaction_data.get("rxn_string", ""),
            "reactants": reaction_data.get("reactants", []),
            "products": reaction_data.get("products", []),
            "score": reaction_data.get("Score", 0.0),
            "reaction_type": reaction_data.get("reaction_name", ""),
            "confidence": reaction_data.get("confidence", 0.0),
            "metadata": {
                "source": reaction_data.get("source", "unknown"),
                "timestamp": time.time(),
                "enriched": reaction_data.get("enriched", False)
            }
        }
        
        # Add any additional fields
        for key, value in reaction_data.items():
            if key not in formatted_data and not key.startswith("_"):
                formatted_data["metadata"][key] = value
        
        return formatted_data
        
    except Exception as e:
        logger.error(f"Error formatting reaction data: {e}")
        return reaction_data


def validate_reaction_smiles(reaction_smiles: str) -> bool:
    """
    Validate reaction SMILES format.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not reaction_smiles or ">>" not in reaction_smiles:
            return False
        
        parts = reaction_smiles.split(">>")
        if len(parts) != 2:
            return False
        
        # Basic validation - could be enhanced
        reactants, products = parts
        return bool(reactants.strip() and products.strip())
        
    except Exception:
        return False


def extract_molecules_from_reaction(reaction_smiles: str) -> Dict[str, list]:
    """
    Extract reactants and products from reaction SMILES.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        Dictionary with 'reactants' and 'products' lists
    """
    try:
        if not validate_reaction_smiles(reaction_smiles):
            return {"reactants": [], "products": []}
        
        reactants_str, products_str = reaction_smiles.split(">>")
        
        reactants = [mol.strip() for mol in reactants_str.split(".") if mol.strip()]
        products = [mol.strip() for mol in products_str.split(".") if mol.strip()]
        
        return {"reactants": reactants, "products": products}
        
    except Exception as e:
        logger.error(f"Error extracting molecules from reaction: {e}")
        return {"reactants": [], "products": []}
