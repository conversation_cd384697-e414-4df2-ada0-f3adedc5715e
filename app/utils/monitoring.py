"""
Monitoring and metrics utilities.
"""

import time
import threading
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from dataclasses import dataclass, field

from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MetricData:
    """Data structure for storing metric information."""
    count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    def add_measurement(self, duration: float):
        """Add a new measurement."""
        self.count += 1
        self.total_time += duration
        self.min_time = min(self.min_time, duration)
        self.max_time = max(self.max_time, duration)
        self.recent_times.append(duration)
    
    @property
    def average_time(self) -> float:
        """Calculate average time."""
        return self.total_time / self.count if self.count > 0 else 0.0
    
    @property
    def recent_average(self) -> float:
        """Calculate recent average time."""
        if not self.recent_times:
            return 0.0
        return sum(self.recent_times) / len(self.recent_times)


class MetricsCollector:
    """Collector for application metrics."""
    
    def __init__(self):
        """Initialize metrics collector."""
        self._metrics: Dict[str, MetricData] = defaultdict(MetricData)
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._lock = threading.Lock()
    
    def time_operation(self, operation_name: str):
        """
        Context manager for timing operations.
        
        Args:
            operation_name: Name of the operation to time
        """
        return TimingContext(self, operation_name)
    
    def record_timing(self, operation_name: str, duration: float):
        """
        Record timing for an operation.
        
        Args:
            operation_name: Name of the operation
            duration: Duration in seconds
        """
        with self._lock:
            self._metrics[operation_name].add_measurement(duration)
    
    def increment_counter(self, counter_name: str, value: int = 1):
        """
        Increment a counter.
        
        Args:
            counter_name: Name of the counter
            value: Value to increment by
        """
        with self._lock:
            self._counters[counter_name] += value
    
    def set_gauge(self, gauge_name: str, value: float):
        """
        Set a gauge value.
        
        Args:
            gauge_name: Name of the gauge
            value: Value to set
        """
        with self._lock:
            self._gauges[gauge_name] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get all collected metrics.
        
        Returns:
            Dictionary of all metrics
        """
        with self._lock:
            metrics = {}
            
            # Timing metrics
            for name, data in self._metrics.items():
                metrics[f"{name}_count"] = data.count
                metrics[f"{name}_total_time"] = data.total_time
                metrics[f"{name}_avg_time"] = data.average_time
                metrics[f"{name}_min_time"] = data.min_time if data.min_time != float('inf') else 0.0
                metrics[f"{name}_max_time"] = data.max_time
                metrics[f"{name}_recent_avg"] = data.recent_average
            
            # Counters
            for name, value in self._counters.items():
                metrics[f"counter_{name}"] = value
            
            # Gauges
            for name, value in self._gauges.items():
                metrics[f"gauge_{name}"] = value
            
            return metrics
    
    def get_summary(self) -> str:
        """
        Get a summary of metrics as a formatted string.
        
        Returns:
            Formatted metrics summary
        """
        metrics = self.get_metrics()
        
        summary_lines = ["=== Metrics Summary ==="]
        
        # Group by operation
        operations = set()
        for key in metrics.keys():
            if '_count' in key:
                operations.add(key.replace('_count', ''))
        
        for op in sorted(operations):
            count = metrics.get(f"{op}_count", 0)
            if count > 0:
                avg_time = metrics.get(f"{op}_avg_time", 0)
                min_time = metrics.get(f"{op}_min_time", 0)
                max_time = metrics.get(f"{op}_max_time", 0)
                recent_avg = metrics.get(f"{op}_recent_avg", 0)
                
                summary_lines.append(f"{op}:")
                summary_lines.append(f"  Count: {count}")
                summary_lines.append(f"  Avg Time: {avg_time:.3f}s")
                summary_lines.append(f"  Min Time: {min_time:.3f}s")
                summary_lines.append(f"  Max Time: {max_time:.3f}s")
                summary_lines.append(f"  Recent Avg: {recent_avg:.3f}s")
        
        # Counters
        counters = {k: v for k, v in metrics.items() if k.startswith('counter_')}
        if counters:
            summary_lines.append("\nCounters:")
            for name, value in sorted(counters.items()):
                summary_lines.append(f"  {name.replace('counter_', '')}: {value}")
        
        # Gauges
        gauges = {k: v for k, v in metrics.items() if k.startswith('gauge_')}
        if gauges:
            summary_lines.append("\nGauges:")
            for name, value in sorted(gauges.items()):
                summary_lines.append(f"  {name.replace('gauge_', '')}: {value}")
        
        return "\n".join(summary_lines)
    
    def reset(self):
        """Reset all metrics."""
        with self._lock:
            self._metrics.clear()
            self._counters.clear()
            self._gauges.clear()


class TimingContext:
    """Context manager for timing operations."""
    
    def __init__(self, collector: MetricsCollector, operation_name: str):
        """
        Initialize timing context.
        
        Args:
            collector: Metrics collector instance
            operation_name: Name of the operation to time
        """
        self.collector = collector
        self.operation_name = operation_name
        self.start_time: Optional[float] = None
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop timing and record result."""
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.collector.record_timing(self.operation_name, duration)


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None
_collector_lock = threading.Lock()


def get_metrics_collector() -> MetricsCollector:
    """
    Get the global metrics collector instance.
    
    Returns:
        MetricsCollector instance
    """
    global _metrics_collector
    
    if _metrics_collector is None:
        with _collector_lock:
            if _metrics_collector is None:
                _metrics_collector = MetricsCollector()
    
    return _metrics_collector


def time_operation(operation_name: str):
    """
    Decorator for timing function execution.
    
    Args:
        operation_name: Name of the operation to time
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            collector = get_metrics_collector()
            with collector.time_operation(operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def increment_counter(counter_name: str, value: int = 1):
    """
    Increment a global counter.
    
    Args:
        counter_name: Name of the counter
        value: Value to increment by
    """
    collector = get_metrics_collector()
    collector.increment_counter(counter_name, value)


def set_gauge(gauge_name: str, value: float):
    """
    Set a global gauge value.
    
    Args:
        gauge_name: Name of the gauge
        value: Value to set
    """
    collector = get_metrics_collector()
    collector.set_gauge(gauge_name, value)


def get_metrics_summary() -> str:
    """
    Get a summary of all metrics.
    
    Returns:
        Formatted metrics summary
    """
    collector = get_metrics_collector()
    return collector.get_summary()
