"""
Azure utilities for chemical reactions and blob storage.
Moved from azure_utils/utils.py with updated imports.
"""

import hashlib
import os
import tempfile
from typing import Optional

from dotenv import load_dotenv
from indigo import Indigo
from indigo.renderer import IndigoRenderer
from azure.storage.blob import BlobServiceClient

from app.core.config import get_config
from app.core.logging import get_logger
import logging

azure_logger = logging.getLogger("azure")
azure_logger.setLevel(logging.WARNING)

load_dotenv()

logger = get_logger(__name__)

class AzureUtils:
    """
    Azure utility class for handling chemical reactions and rendering images.
    
    This class provides methods to load chemical reactions, render them as images, 
    and upload these images to Azure Blob Storage.
    """
    
    def __init__(self, config=None):
        """
        Initialize the AzureUtils class.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or get_config()
        self.indigo = Indigo()
        self.renderer = IndigoRenderer(self.indigo)
        self.connection_string = getattr(self.config.api, 'azure_blob_connection_string', None)
        self.container_name = getattr(self.config.api, 'azure_blob_container_name', 'imagestorage')

    def upload_bytes_to_azure_blob(self, image_bytes: bytes, blob_name: str) -> Optional[str]:
        """
        Upload image bytes to Azure Blob Storage.
        
        Args:
            image_bytes: Image data as bytes
            blob_name: Name for the blob
            
        Returns:
            Blob URL if successful, None otherwise
        """
        try:
            if not self.connection_string:
                logger.warning("Azure Blob connection string not configured")
                return None
            
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            container_client = blob_service_client.get_container_client(self.container_name)

            blob_client = container_client.get_blob_client(blob_name)
            blob_client.upload_blob(
                image_bytes, 
                overwrite=True, 
                content_type='image/png'
            )

            blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{self.container_name}/{blob_name}"
            logger.info(f"Successfully uploaded blob: {blob_url}")
            return blob_url
            
        except Exception as e:
            logger.error(f"Failed to upload blob {blob_name}: {e}")
            return None

    def render_reaction_to_image(self, reaction_smiles: str) -> Optional[bytes]:
        """
        Render reaction SMILES to image bytes.
        
        Args:
            reaction_smiles: Reaction SMILES string
            
        Returns:
            Image bytes if successful, None otherwise
        """
        try:
            reaction = self.indigo.loadReaction(reaction_smiles)
            self.indigo.setOption("render-output-format", "png")
            self.indigo.setOption("render-image-size", 800, 600)
            self.indigo.setOption("render-margins", 20, 20)
            self.indigo.setOption("render-coloring", True)
            
            image_bytes = self.renderer.renderToBuffer(reaction)
            logger.debug(f"Successfully rendered reaction: {reaction_smiles[:50]}...")
            return image_bytes
            
        except Exception as e:
            logger.error(f"Failed to render reaction {reaction_smiles}: {e}")
            return None

    def generate_reaction_image_url(self, reaction_smiles: str) -> Optional[str]:
        """
        Generate and upload reaction image, return URL.
        
        Args:
            reaction_smiles: Reaction SMILES string
            
        Returns:
            Image URL if successful, None otherwise
        """
        try:
            # Generate unique blob name based on reaction SMILES
            blob_name = self._generate_blob_name(reaction_smiles)
            
            # Check if image already exists (optional optimization)
            # For now, always generate new image
            
            # Render reaction to image
            image_bytes = self.render_reaction_to_image(reaction_smiles)
            if not image_bytes:
                return None
            
            # Upload to Azure Blob Storage
            blob_url = self.upload_bytes_to_azure_blob(image_bytes, blob_name)
            return blob_url
            
        except Exception as e:
            logger.error(f"Failed to generate reaction image URL: {e}")
            return None

    def _generate_blob_name(self, reaction_smiles: str) -> str:
        """
        Generate unique blob name for reaction SMILES.
        
        Args:
            reaction_smiles: Reaction SMILES string
            
        Returns:
            Unique blob name
        """
        # Create hash of reaction SMILES for unique filename
        hash_object = hashlib.md5(reaction_smiles.encode())
        hash_hex = hash_object.hexdigest()
        return f"reactions/{hash_hex}.png"

    def render_molecule_to_image(self, molecule_smiles: str) -> Optional[bytes]:
        """
        Render molecule SMILES to image bytes.
        
        Args:
            molecule_smiles: Molecule SMILES string
            
        Returns:
            Image bytes if successful, None otherwise
        """
        try:
            molecule = self.indigo.loadMolecule(molecule_smiles)
            self.indigo.setOption("render-output-format", "png")
            self.indigo.setOption("render-image-size", 400, 400)
            self.indigo.setOption("render-margins", 20, 20)
            self.indigo.setOption("render-coloring", True)
            
            image_bytes = self.renderer.renderToBuffer(molecule)
            logger.debug(f"Successfully rendered molecule: {molecule_smiles[:50]}...")
            return image_bytes
            
        except Exception as e:
            logger.error(f"Failed to render molecule {molecule_smiles}: {e}")
            return None

    def generate_molecule_image_url(self, molecule_smiles: str) -> Optional[str]:
        """
        Generate and upload molecule image, return URL.
        
        Args:
            molecule_smiles: Molecule SMILES string
            
        Returns:
            Image URL if successful, None otherwise
        """
        try:
            # Generate unique blob name based on molecule SMILES
            hash_object = hashlib.md5(molecule_smiles.encode())
            hash_hex = hash_object.hexdigest()
            blob_name = f"molecules/{hash_hex}.png"
            
            # Render molecule to image
            image_bytes = self.render_molecule_to_image(molecule_smiles)
            if not image_bytes:
                return None
            
            # Upload to Azure Blob Storage
            blob_url = self.upload_bytes_to_azure_blob(image_bytes, blob_name)
            return blob_url
            
        except Exception as e:
            logger.error(f"Failed to generate molecule image URL: {e}")
            return None

    def cleanup_temp_files(self):
        """Clean up any temporary files created during rendering."""
        try:
            # Clean up any temporary files if needed
            # This is a placeholder for any cleanup logic
            pass
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")

    def test_connection(self) -> bool:
        """
        Test Azure Blob Storage connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if not self.connection_string:
                logger.warning("Azure Blob connection string not configured")
                return False
            
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            container_client = blob_service_client.get_container_client(self.container_name)
            
            # Try to list blobs (this will test the connection)
            list(container_client.list_blobs(max_results=1))
            logger.info("Azure Blob Storage connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Azure Blob Storage connection test failed: {e}")
            return False

    def image_to_blob(self, reaction_smiles: str) -> Optional[str]:
        """
        Legacy method name for generate_reaction_image_url.
        Maintained for backward compatibility.

        Args:
            reaction_smiles: Reaction SMILES string

        Returns:
            URL of uploaded image or None if failed
        """
        return self.generate_reaction_image_url(reaction_smiles)
