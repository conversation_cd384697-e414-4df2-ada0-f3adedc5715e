"""
Retro synthesis service for handling retro synthesis operations.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>
from app.core.config import get_config
from app.core.logging import get_logger
from app.core.exceptions import ProcessingError

logger = get_logger(__name__)


class RetroSynthesisService:
    """Service for handling retro synthesis operations."""
    
    def __init__(self, config=None):
        """
        Initialize retro synthesis service.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or get_config()
    
    def run_retro_synthesis(
        self,
        target_smiles: str,
        request_id: str,
        kwargs: Dict[str, Any]
    ) -> Tuple[int, str]:
        """
        Run retro synthesis for target molecule.
        
        Args:
            target_smiles: Target molecule SMILES
            request_id: Request identifier
            kwargs: Additional parameters
            
        Returns:
            Tuple of (total_routes, message)
        """
        try:
            # Import here to avoid circular imports
            from app.engines.retro_runner import retro_runner

            logger.info(f"Starting retro synthesis for {target_smiles} with request_id {request_id}")

            # Run the retro synthesis
            total_routes, message = retro_runner(target_smiles, request_id=request_id, kwargs=kwargs)
            
            logger.info(f"Completed retro synthesis for {target_smiles}: {total_routes} routes found")
            
            return total_routes, message
            
        except Exception as e:
            logger.error(f"Error in retro synthesis for {target_smiles}: {e}")
            raise ProcessingError(f"Retro synthesis failed: {e}")
    
    def validate_smiles(self, smiles: str) -> bool:
        """
        Validate SMILES string.
        
        Args:
            smiles: SMILES string to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            from rdkit import Chem
            mol = Chem.MolFromSmiles(smiles)
            return mol is not None
        except Exception as e:
            logger.warning(f"SMILES validation failed for {smiles}: {e}")
            return False
    
    def canonicalize_smiles(self, smiles: str) -> str:
        """
        Canonicalize SMILES string.
        
        Args:
            smiles: SMILES string to canonicalize
            
        Returns:
            Canonicalized SMILES string
        """
        try:
            from app.utils.chemistry import canonicalize_smiles
            return canonicalize_smiles(smiles)
        except Exception as e:
            logger.error(f"SMILES canonicalization failed for {smiles}: {e}")
            raise ProcessingError(f"SMILES canonicalization failed: {e}")
