"""
Database service for handling all database operations.
"""

import uuid
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from pymongo import MongoClient
from pymongo.errors import PyMongoError

from app.core.config import get_config
from app.core.logging import get_logger
from app.core.exceptions import DatabaseError

logger = get_logger(__name__)


class RetroStatus(Enum):
    """Enum to represent the status of the retro synthesis process."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class DatabaseService:
    """Service for handling database operations."""
    
    def __init__(self, config=None):
        """
        Initialize database service.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or get_config()
        self._client = None
        self._db = None
        self._lock = threading.Lock()
        
        # Initialize connection
        self._connect()
    
    def _connect(self):
        """Establish database connection."""
        try:
            self._client = MongoClient(self.config.database.mongo_url)
            self._db = self._client[self.config.database.database_name]
            
            # Test connection
            self._client.admin.command('ping')
            logger.info("Database connection established")
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise DatabaseError(f"Database connection failed: {e}")

    @property
    def db(self):
        """Property for backward compatibility with original DbOps interface."""
        return self._db

    @property
    def db(self):
        """Get database instance."""
        if self._db is None:
            with self._lock:
                if self._db is None:
                    self._connect()
        return self._db
    
    @property
    def client(self):
        """Get client instance."""
        if self._client is None:
            with self._lock:
                if self._client is None:
                    self._connect()
        return self._client
    
    def insert_log(
        self,
        request_id: str,
        input_type: str,
        input_value: str,
        status: RetroStatus,
        user_id: str = '',
        tenant_id: str = '',
        error_message: Optional[str] = None
    ) -> str:
        """
        Insert or update a log entry.
        
        Args:
            request_id: Request identifier
            input_type: Type of input (e.g., "SMILES")
            input_value: Input value
            status: Processing status
            user_id: User identifier
            tenant_id: Tenant identifier
            error_message: Optional error message
            
        Returns:
            Document ID
        """
        try:
            current_time = datetime.utcnow()
            
            log_doc = {
                '_id': str(uuid.uuid4()),
                'request_id': request_id,
                'input_type': input_type,
                'input_value': input_value,
                'status': status.value,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'created_at': current_time,
                'updated_at': current_time,
                'error_message': error_message
            }
            
            # Check if entry exists
            existing_entry = self._db['retro_requests'].find_one({'request_id': request_id})

            if existing_entry:
                # Update existing entry
                update_doc = {
                    'status': status.value,
                    'updated_at': current_time
                }
                if error_message:
                    update_doc['error_message'] = error_message

                self._db['retro_requests'].update_one(
                    {'request_id': request_id},
                    {'$set': update_doc}
                )
                logger.debug(f"Updated log entry for request {request_id}")
                return existing_entry['_id']
            else:
                # Insert new entry
                result = self._db['retro_requests'].insert_one(log_doc)
                logger.debug(f"Inserted new log entry for request {request_id}")
                return str(result.inserted_id)
                
        except PyMongoError as e:
            logger.error(f"Database error in insert_log: {e}")
            raise DatabaseError(f"Failed to insert log: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in insert_log: {e}")
            raise DatabaseError(f"Unexpected error in insert_log: {e}")
    
    def insert_config_history(
        self,
        request_id: str,
        config: Dict[str, Any],
        status: RetroStatus
    ) -> str:
        """
        Insert configuration history.
        
        Args:
            request_id: Request identifier
            config: Configuration dictionary
            status: Processing status
            
        Returns:
            Document ID
        """
        try:
            current_time = datetime.utcnow()
            
            config_doc = {
                '_id': str(uuid.uuid4()),
                'request_id': request_id,
                'config': config,
                'status': status.value,
                'created_at': current_time,
                'updated_at': current_time
            }
            
            result = self._db['config_history'].insert_one(config_doc)
            logger.debug(f"Inserted config history for request {request_id}")
            return str(result.inserted_id)
            
        except PyMongoError as e:
            logger.error(f"Database error in insert_config_history: {e}")
            raise DatabaseError(f"Failed to insert config history: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in insert_config_history: {e}")
            raise DatabaseError(f"Unexpected error in insert_config_history: {e}")
    
    def check_config_is_processed(
        self,
        request_id: str,
        config: Dict[str, Any]
    ) -> bool:
        """
        Check if configuration is already processed.
        
        Args:
            request_id: Request identifier
            config: Configuration to check
            
        Returns:
            True if already processed, False otherwise
        """
        try:
            existing_config = self._db['config_history'].find_one({
                'request_id': request_id,
                'config': config,
                'status': RetroStatus.COMPLETED.value
            })
            
            return existing_config is not None
            
        except PyMongoError as e:
            logger.error(f"Database error in check_config_is_processed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in check_config_is_processed: {e}")
            return False
    
    def insert_retro_data(
        self,
        request_id: str,
        unique_id: int,
        route_id: int,
        num_steps: int,
        total_route_score: float,
        target_smiles: str,
        total_cost: float,
        raw_smiles: List[str],
        route_level_image: str,
        route_name: str,
        data: List[Dict[str, Any]],
        level: int,
        config: Dict[str, Any]
    ) -> str:
        """
        Insert retro synthesis data into the database.
        Matches the original DbOps.insert_retro_data signature exactly.
        """
        logger.info(f"Inserting retro data for request_id: {request_id}, unique_id: {unique_id}, route_id: {route_id}")
        try:
            current_time = datetime.utcnow()
            generated_id = str(uuid.uuid4())

            data_doc = {
                '_id': generated_id,
                'request_id': request_id,
                'route_id': route_id,
                'unique_id': unique_id,
                'target_smiles': target_smiles,
                'route_name': route_name,
                'route_reaction_img': route_level_image,
                'raw_reactants': raw_smiles,
                'num_steps': num_steps,
                'total_route_score': total_route_score,
                'total_cost': total_cost,
                'data': data,
                'route_status': 'IN_PROGRESS',
                'config': config,
                'level': level,
                'created_at': current_time,
                'updated_at': current_time
            }

            result = self._db['retro_data'].update_one(
                {'unique_id': unique_id},
                {
                    '$set': {'updated_at': current_time},
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k != 'updated_at'}
                },
                upsert=True
            )

            if result.upserted_id:
                logger.info(f"Inserted new retro data for unique_id: {unique_id}")
                return str(result.upserted_id)
            else:
                logger.info(f"Updated existing retro data for unique_id: {unique_id}")
                # Retrieve the _id of the existing document
                existing_doc = self._db['retro_data'].find_one({'unique_id': unique_id}, {'_id': 1})
                if existing_doc:
                    return str(existing_doc['_id'])
                else:
                    logger.error(f"Could not find existing retro data after update for unique_id: {unique_id}")
                    return None

        except Exception as e:
            logger.error(f"Failed to upsert retro data for unique_id {unique_id}: {str(e)}")
            return None
    
    def fetch_latest_retro_request_id_by_smiles(self, target_smiles: str) -> Optional[str]:
        """
        Fetch latest request ID by target SMILES.
        
        Args:
            target_smiles: Target SMILES string
            
        Returns:
            Latest request ID or None
        """
        try:
            latest_request = self._db['retro_requests'].find_one(
                {'input_value': target_smiles, 'status': RetroStatus.COMPLETED.value},
                sort=[('created_at', -1)]
            )
            
            return latest_request['request_id'] if latest_request else None
            
        except PyMongoError as e:
            logger.error(f"Database error in fetch_latest_retro_request_id_by_smiles: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in fetch_latest_retro_request_id_by_smiles: {e}")
            return None
    
    def fetch_retro_data_by_request_id_and_target_smiles(
        self,
        request_id: str,
        target_smiles: str
    ) -> List[Dict[str, Any]]:
        """
        Fetch retro data by request ID and target SMILES.
        
        Args:
            request_id: Request identifier
            target_smiles: Target SMILES string
            
        Returns:
            List of retro data documents
        """
        try:
            retro_data = list(self._db['retro_data'].find({'request_id': request_id}))
            return retro_data
            
        except PyMongoError as e:
            logger.error(f"Database error in fetch_retro_data_by_request_id_and_target_smiles: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error in fetch_retro_data_by_request_id_and_target_smiles: {e}")
            return []

    def insert_config_history(self, request, config, status):
        """
        Insert retro synthesis config history into the database.
        """
        logger.info(f"Inserting config history for request_id: {request}")
        current_time = datetime.utcnow()

        data_doc = {
            '_id': str(uuid.uuid4()),
            'request_id': request,
            'config': config,
            'status': status.value,
            'created_at': current_time,
            'updated_at': current_time
        }

        try:
            result = self._db['retro_config_history'].insert_one(data_doc)
            logger.info(f"Inserted config history for request_id: {request}")
            return result.inserted_id
        except Exception as e:
            logger.error(f"Failed to insert config history for request_id {request}: {str(e)}")
            return None

    def get2_top_routes(self, request_id, limit=100):
        """Get top routes with empty other_information."""
        query = {
            'request_id': request_id,
            'data': {
                '$elemMatch': {
                    'other_information': {}
                }
            }
        }
        projection = {
            '_id': 1,
            'route_id': 1,
            'num_steps': 1,
            'total_route_score': 1,
            'data': 1
        }

        routes = list(self._db['retro_data'].find(query, projection).sort('total_route_score', -1).limit(limit))

        # Optional: narrow to only steps with empty `other_information`
        for route in routes:
            route['data'] = [step for step in route.get('data', []) if step.get('other_information') == {} or step.get('other_information') is None]

        return routes

    def get_top_routes(self, limit=100):
        """
        Retrieve the top routes for a given target SMILES.
        """
        query = {
             'route_status': 'IN_PROGRESS'
        }
        routes = list(self._db['retro_data'].find(query).sort([('created_at', -1), ('total_route_score', -1)]))
        return routes

    def get_route_by_id(self, route_id):
        """
        Retrieve route by ID.
        """
        query = {
             'route_status': 'IN_PROGRESS' , '_id' : route_id
        }

        routes = list(self._db['retro_data'].find(query).sort([('created_at', -1), ('total_route_score', -1)]))
        return routes

    def update_retro_data(self, id, data):
        """
        Update retro synthesis data in the database.
        """
        current_time = datetime.utcnow()

        result = self._db['retro_data'].update_one(
            {'_id': id},
            {'$set': {'data': data, 'updated_at': current_time , 'route_status' : 'COMPLETED'}}
        )

        if result.modified_count > 0:
            logger.info(f"Updated retro data for id: {id}")
        else:
            logger.warning(f"No updates made for id: {id}")

    def check_request_id_is_processing(self, request_id):
        """Check if request ID is already processing."""
        existing_doc = self._db['retro_requests'].find_one(
            { 'request_id': request_id}
        )
        if existing_doc:
            logger.info(f"Request already processed: {request_id}")
            return True
        return False

    def close(self):
        """Close database connection."""
        if self._client:
            self._client.close()
            logger.info("Database connection closed")
