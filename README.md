# 🧪 Retro Synthesis Runner !!

A comprehensive, multi-step retro-synthesis tree search system for discovering synthetic routes to target molecules using SMILES notation.

## 🎯 Overview

The Retro Synthesis Runner is an advanced AI-powered system that finds optimal synthetic pathways for target molecules. It uses state-of-the-art machine learning models and breadth-first search algorithms to explore the chemical space and identify feasible synthesis routes.

### 🔬 What is Retro Synthesis?

Retro synthesis is the process of working backwards from a target molecule to identify simpler starting materials and the reactions needed to synthesize the target. This system:

- **Input**: Target molecule as SMILES string (e.g., `CCO` for ethanol)
- **Output**: Ranked synthesis routes with reaction steps, reagents, and scores
- **Process**: Uses AI models to predict disconnections and evaluate synthetic feasibility

### 🧬 SMILES Support

**SMILES (Simplified Molecular Input Line Entry System)** is a chemical notation that describes molecular structures as text strings.

**Examples:**
- `CCO` - Ethanol
- `CC(=O)O` - Acetic acid
- `c1ccccc1` - Benzene
- `CC(C)(C)c1ccc(O)cc1` - 4-tert-butylphenol

**Supported Features:**
- ✅ Canonical SMILES normalization
- ✅ Stereochemistry handling
- ✅ Complex organic molecules
- ✅ Multi-component reactions
- ✅ Building block recognition
- ✅ Synthesis score calculation

## 🏗️ Architecture & Design

### 1. **Production-Ready Structure**
```
app/
├── core/                    # Core configuration and utilities
│   ├── config.py           # Centralized configuration management
│   ├── logging.py          # Structured logging system
│   └── exceptions.py       # Custom exception hierarchy
├── engines/                # Retro synthesis engines
│   ├── bfs_tree_search_engine.py  # Main BFS search algorithm
│   ├── tree_search_engine.py      # Alternative search engine
│   └── retro_runner.py            # Main runner interface
├── services/               # Business logic services
│   ├── database_service.py        # MongoDB operations
│   ├── retro_service.py           # Retro synthesis service
│   └── azure/utils.py             # Azure ML integrations
├── workers/                # Celery task workers
│   ├── celery_app.py              # Celery application
│   ├── retro_worker.py            # Main retro synthesis worker
│   └── enrich_worker.py           # Route enrichment worker
├── utils/                  # Utility functions
│   ├── chemistry.py               # Chemistry utilities
│   ├── common.py                  # Common utilities
│   └── monitoring.py              # System monitoring
└── infra/                  # Infrastructure components
    └── db/disconnections_fileStorage.py
```

### 2. **Key Components**

**Tree Nodes:**
- `MoleculeNode` (OR node): Represents molecules, solved if any reaction path works
- `ReactionNode` (AND node): Represents reactions, solved only if all precursors are solved

**AI Models Integration:**
- **Azure ML Endpoints**: Disconnection tagging, reactant generation, retro ranking
- **Parrot API**: Reagent prediction for synthesis routes
- **AskCos APIs**: Multiple retro synthesis prediction models
- **SCScore**: Synthesis complexity scoring

**Validation & Scoring:**
- `TerminalChecker`: Determines when molecules are "solved" (building blocks, depth, SCScore)
- `RouteScorer`: Ranks synthesis routes based on multiple criteria
- `DisconnectionValidator`: Validates reaction feasibility

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- Docker & Docker Compose
- MongoDB (for data storage)
- Redis (for task queuing)

### 1. **Clone and Setup**
```bash
git clone <repository-url>
cd retro-runner
cp .env.template .env
# Edit .env with your API keys and configuration
```

### 2. **Docker Deployment (Recommended)**
```bash
# Start the complete stack
docker-compose up -d

# Check service health
docker-compose ps
```

### 3. **Local Development**
```bash
# Install dependencies
pip install -r requirements.txt

# Start services
python main.py worker    # Start Celery worker
python main.py server    # Start web server
python main.py health    # Run health checks
```

## 💡 Usage Examples

### 1. **Basic SMILES Processing**
```python
from app.engines.retro_runner import run_retro_synthesis

# Simple molecule - Ethanol
target_smiles = "CCO"
routes = run_retro_synthesis(
    target_smiles=target_smiles,
    max_depth=3,
    beam_width=10
)

print(f"Found {len(routes)} synthesis routes for {target_smiles}")
```

### 2. **Complex Molecule Synthesis**
```python
# Complex pharmaceutical intermediate
target_smiles = "CC(C)(C)c1ccc(O)cc1C(=O)Nc2ccccc2"

routes = run_retro_synthesis(
    target_smiles=target_smiles,
    max_depth=4,
    beam_width=15,
    max_routes=50
)

# Routes are ranked by synthesis score (lower = better)
for i, route in enumerate(routes[:5]):
    print(f"Route {i+1}: {len(route)} steps")
    for step, reaction in enumerate(route):
        print(f"  Step {step+1}: {reaction.reaction_data['Retro']}")
```


## 🔧 Configuration

### Environment Variables
```bash
# Database
MONGODB_URL=**************************************/
REDIS_URL=redis://localhost:6379/0

# AI Model APIs
AZURE_DISCONNECTION_TAGGER_URL=https://your-endpoint.azureml.net/score
AZURE_DISCONNECTION_TAGGER_API_KEY=your-api-key
PARROT_URL=https://your-parrot-endpoint.com/predict
PARROT_KEY=your-parrot-api-key

# Processing Parameters
MAX_DEPTH=3                    # Maximum search depth
BEAM_WIDTH=10                  # Beam search width
SCSCORE_THRESHOLD=1.25         # Synthesis complexity threshold
PARALLEL_BFS_WORKERS=4         # Parallel worker count
```

### Search Parameters
- **max_depth**: How many reaction steps to explore (1-5 recommended)
- **beam_width**: Number of best reactions to keep at each level (5-20)
- **scscore_threshold**: Molecules below this complexity are considered "easy" (1.0-2.0)
- **max_routes**: Maximum number of routes to return (10-500)

## 🧠 Algorithm Details

### BFS Tree Search Algorithm

The system uses a sophisticated breadth-first search with the following logic:

1. **Molecule Node Expansion:**
   - Check terminal conditions (building blocks, depth limit, SCScore)
   - Call multiple AI models in parallel for disconnection prediction
   - Generate and validate potential reaction pathways
   - Create reaction nodes for feasible disconnections

2. **Reaction Node Expansion:**
   - Parse reactants from reaction SMILES
   - Create molecule nodes for each reactant
   - Apply beam search to limit exploration
   - Mark as solved when all reactants are solved

3. **Route Extraction & Scoring:**
   - DFS traversal to find complete synthesis paths
   - Score routes using multiple criteria:
     - Synthesis complexity (SCScore)
     - Reaction feasibility (ML predictions)
     - Route length and efficiency
   - Rank and return top routes

### AI Model Pipeline

```mermaid
graph TD
    A[Target SMILES] --> B[Disconnection Tagging]
    B --> C[Reactant Generation]
    C --> D[Retro Ranking]
    D --> E[Route Scoring]
    E --> F[Parrot Reagent Prediction]
    F --> G[Final Ranked Routes]
```

**Model Integration:**
- **Azure ML**: Disconnection tagging, reactant generation, retro ranking
- **Parrot**: Reagent and condition prediction
- **AskCos**: Multiple retro synthesis models (EM, AT, TR)
- **SCScore**: Synthesis complexity assessment

## 📊 Output Format

### Route Structure
```json
{
  "target_smiles": "CCO",
  "num_routes": 3,
  "routes": [
    {
      "route_id": 1,
      "num_steps": 2,
      "total_route_score": 0.85,
      "reactions": [
        {
          "step": 1,
          "reaction_string": "CC=O.H>>CCO",
          "retro_smiles": "CC=O.H",
          "reagents": "NaBH4, MeOH",
          "score": 0.92,
          "rxn_class": {
            "reaction_name": "Carbonyl Reduction",
            "confidence": 0.95
          }
        }
      ]
    }
  ]
}
```

### SMILES Processing Features

**Input Validation:**
- Canonical SMILES normalization
- Stereochemistry preservation
- Invalid structure detection
- Molecular weight limits

**Output Quality:**
- Reaction feasibility scores
- Reagent predictions
- Synthesis complexity assessment
- Route diversity ranking

## 🔍 Monitoring & Debugging

### Health Checks
```bash
# System health
python main.py health

# Service status
python main.py status

# Configuration validation
python main.py validate
```

### Monitoring Dashboard
- **Flower UI**: `http://localhost:5555` - Celery task monitoring
- **Health Endpoint**: `http://localhost:8080/health` - Service health
- **Logs**: `logs/retro_synthesis.log` - Detailed application logs

### Performance Metrics
- **Throughput**: Molecules processed per hour
- **Success Rate**: Percentage of molecules with found routes
- **Average Route Quality**: Mean synthesis scores
- **API Response Times**: Model inference latency

## 🛠️ Development

### Adding New Models
```python
# Extend the BFS engine with new AI models
class CustomRetroModel:
    def predict_disconnections(self, smiles: str):
        # Your model implementation
        return predictions

# Register in the engine
engine.add_model("custom_model", CustomRetroModel())
```

### Custom Scoring
```python
# Implement custom route scoring
class CustomRouteScorer:
    def score_route(self, route: List[ReactionNode]) -> float:
        # Your scoring logic
        return score

# Use in the engine
engine.route_scorer = CustomRouteScorer()
```

## 🚨 Troubleshooting

### Common Issues

**1. API Key Errors**
```bash
# Check configuration
python main.py validate
# Update .env file with correct API keys
```

**2. Memory Issues**
```bash
# Reduce parallel workers
export PARALLEL_BFS_WORKERS=2
# Reduce beam width
export BEAM_WIDTH=5
```

**3. Database Connection**
```bash
# Check MongoDB status
docker-compose ps mongodb
# Restart if needed
docker-compose restart mongodb
```

**4. Invalid SMILES**
```python
from app.utils.chemistry import canonicalize_smiles

# Validate SMILES before processing
canonical = canonicalize_smiles("your_smiles_here")
if canonical:
    print(f"Valid SMILES: {canonical}")
else:
    print("Invalid SMILES structure")
```

## 📈 Performance Optimization

### Scaling Guidelines
- **Small molecules** (< 20 atoms): `max_depth=2`, `beam_width=10`
- **Medium molecules** (20-50 atoms): `max_depth=3`, `beam_width=15`
- **Large molecules** (> 50 atoms): `max_depth=4`, `beam_width=20`

## 📚 References

- **RDKit**: Chemical informatics toolkit
- **SCScore**: Synthesis complexity prediction
- **AskCos**: MIT retro synthesis platform
- **Azure ML**: Microsoft machine learning platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

---

**🧪 Ready to discover new synthesis routes? Start with a simple SMILES and explore the chemical space!**
