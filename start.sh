#!/bin/bash

# Retro Synthesis Runner Startup Script
echo "Starting Retro Synthesis Runner..."

# Set environment variables
export PYTHONPATH=/app
export PYTHONUNBUFFERED=1

# Create necessary directories
mkdir -p logs data/building_blocks bfs_interim_results


# Start Celery workers in the background
echo "Starting Celery workers..."

# Main retro synthesis worker
celery -A app.workers.celery_app worker \
    --loglevel=INFO \
    --concurrency=1 \
    --pool=prefork \
    --hostname=retro@%h &

# Enrichment worker
celery -A app.workers.celery_app worker \
    --loglevel=INFO \
    --concurrency=6 \
    --pool=prefork \
    --hostname=enrichment@%h &

# Start the main application server
echo "Starting main application server..."
python main.py server &

# Wait a bit for services to start
sleep 5

# Run health checks
echo "Running health checks..."
python main.py health

# Keep script running and monitor processes
echo "All services started. Monitoring..."
wait